<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-eye text-primary"></i> 配方详情</h2>
        <p class="text-muted mb-0">查看配方的详细信息和原材料配比</p>
    </div>
    <div>
        <a href="index.php?controller=recipe&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        <a href="index.php?controller=recipe&action=edit&id=<?php echo $recipe['id']; ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑配方
        </a>
    </div>
</div>

<div class="row">
    <!-- 配方基本信息 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>成品名称:</strong></td>
                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>成品规格:</strong></td>
                        <td><?php echo htmlspecialchars($product['specification'] ?? '-'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>成品单位:</strong></td>
                        <td><?php echo htmlspecialchars($product['unit']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>配方名称:</strong></td>
                        <td>
                            <?php if (!empty($recipe['name'])): ?>
                                <?php echo htmlspecialchars($recipe['name']); ?>
                            <?php else: ?>
                                <span class="text-muted">默认配方</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>创建时间:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($recipe['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <td><strong>最后更新:</strong></td>
                        <td>
                            <?php if ($recipe['updated_at'] !== $recipe['created_at']): ?>
                                <?php echo date('Y-m-d H:i:s', strtotime($recipe['updated_at'])); ?>
                            <?php else: ?>
                                <span class="text-muted">未更新</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 成本分析 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> 成本分析
                </h5>
            </div>
            <div class="card-body">
                <?php 
                $totalCost = 0;
                $canCalculate = true;
                foreach ($recipe['items'] as $item) {
                    if ($item['material_unit_price']) {
                        $totalCost += $item['quantity_needed'] * $item['material_unit_price'];
                    } else {
                        $canCalculate = false;
                    }
                }
                ?>
                
                <table class="table table-borderless">
                    <tr>
                        <td><strong>原材料种类:</strong></td>
                        <td><?php echo count($recipe['items']); ?> 种</td>
                    </tr>
                    <tr>
                        <td><strong>生产成本:</strong></td>
                        <td>
                            <?php if ($canCalculate): ?>
                                <span class="text-success">¥<?php echo number_format($totalCost, 2); ?></span>
                            <?php else: ?>
                                <span class="text-muted">无法计算（部分原材料未设置单价）</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>销售价格:</strong></td>
                        <td>¥<?php echo number_format($product['selling_price'], 2); ?></td>
                    </tr>
                    <tr>
                        <td><strong>预计利润:</strong></td>
                        <td>
                            <?php if ($canCalculate): ?>
                                <?php $profit = $product['selling_price'] - $totalCost; ?>
                                <span class="<?php echo $profit > 0 ? 'text-success' : 'text-danger'; ?>">
                                    ¥<?php echo number_format($profit, 2); ?>
                                    (<?php echo number_format(($profit / $product['selling_price']) * 100, 1); ?>%)
                                </span>
                            <?php else: ?>
                                <span class="text-muted">无法计算</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 原材料配比详情 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-cubes"></i> 原材料配比
            <small class="text-muted">- 生产1<?php echo htmlspecialchars($product['unit']); ?>所需原材料</small>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($recipe['items'])): ?>
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <h6 class="text-muted">该配方暂无原材料配比</h6>
                <a href="index.php?controller=recipe&action=edit&id=<?php echo $recipe['id']; ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加原材料
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>原材料名称</th>
                            <th>规格</th>
                            <th>所需数量</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>小计成本</th>
                            <th>库存状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recipe['items'] as $item): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($item['material_name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($item['material_specification'] ?? '-'); ?></td>
                                <td><?php echo number_format($item['quantity_needed'], 3); ?></td>
                                <td><?php echo htmlspecialchars($item['unit_of_material']); ?></td>
                                <td>
                                    <?php if ($item['material_unit_price']): ?>
                                        ¥<?php echo number_format($item['material_unit_price'], 2); ?>
                                    <?php else: ?>
                                        <span class="text-muted">未设置</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($item['material_unit_price']): ?>
                                        ¥<?php echo number_format($item['quantity_needed'] * $item['material_unit_price'], 2); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($item['material_stock'] >= $item['quantity_needed']): ?>
                                        <span class="badge bg-success">库存充足</span>
                                        <small class="text-muted d-block">
                                            库存: <?php echo number_format($item['material_stock'], 2); ?><?php echo htmlspecialchars($item['unit_of_material']); ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="badge bg-warning">库存不足</span>
                                        <small class="text-warning d-block">
                                            库存: <?php echo number_format($item['material_stock'], 2); ?><?php echo htmlspecialchars($item['unit_of_material']); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <?php if ($canCalculate): ?>
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="5"><strong>总成本</strong></td>
                                <td><strong>¥<?php echo number_format($totalCost, 2); ?></strong></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    <?php endif; ?>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 生产能力分析 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-industry"></i> 生产能力分析
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>当前库存可生产数量：</h6>
                <?php 
                $maxProduction = PHP_INT_MAX;
                foreach ($recipe['items'] as $item) {
                    if ($item['quantity_needed'] > 0) {
                        $canProduce = floor($item['material_stock'] / $item['quantity_needed']);
                        $maxProduction = min($maxProduction, $canProduce);
                    }
                }
                if ($maxProduction == PHP_INT_MAX) $maxProduction = 0;
                ?>
                <p class="h4 <?php echo $maxProduction > 0 ? 'text-success' : 'text-warning'; ?>">
                    <?php echo $maxProduction; ?> <?php echo htmlspecialchars($product['unit']); ?>
                </p>
            </div>
            <div class="col-md-6">
                <h6>限制因素：</h6>
                <?php 
                $limitingFactor = '';
                $minRatio = PHP_INT_MAX;
                foreach ($recipe['items'] as $item) {
                    if ($item['quantity_needed'] > 0) {
                        $ratio = $item['material_stock'] / $item['quantity_needed'];
                        if ($ratio < $minRatio) {
                            $minRatio = $ratio;
                            $limitingFactor = $item['material_name'];
                        }
                    }
                }
                ?>
                <p class="text-muted">
                    <?php echo $limitingFactor ? htmlspecialchars($limitingFactor) : '无限制因素'; ?>
                </p>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
