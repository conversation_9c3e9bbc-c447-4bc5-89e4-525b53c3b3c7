<?php
/**
 * 原材料管理调试页面
 */

session_start();

echo "<h2>原材料管理调试</h2>";

echo "<h3>步骤1: 检查会话</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ 用户已登录，ID: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "❌ 用户未登录<br>";
    echo '<a href="index.php?controller=auth&action=login">请先登录</a><br>';
    exit;
}

echo "<h3>步骤2: 检查文件</h3>";
$files = [
    '../app/core/Controller.php' => '核心控制器',
    '../app/controllers/MaterialController.php' => '原材料控制器',
    '../app/models/Material.php' => '原材料模型',
    '../app/views/materials/index.php' => '原材料视图'
];

$allFilesExist = true;
foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc}: 存在<br>";
    } else {
        echo "❌ {$desc}: 不存在<br>";
        $allFilesExist = false;
    }
}

if (!$allFilesExist) {
    echo "请检查文件是否存在<br>";
    exit;
}

echo "<h3>步骤3: 测试数据库连接</h3>";
try {
    require_once '../app/core/Database.php';
    $db = Database::getInstance();
    echo "✅ 数据库连接成功<br>";
    
    // 检查materials表是否存在
    $result = $db->fetch("SHOW TABLES LIKE 'materials'");
    if ($result) {
        echo "✅ materials表存在<br>";
        
        // 检查表结构
        $count = $db->fetch("SELECT COUNT(*) as count FROM materials");
        echo "✅ materials表记录数: " . $count['count'] . "<br>";
    } else {
        echo "❌ materials表不存在，请运行install.php初始化数据库<br>";
        exit;
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h3>步骤4: 测试Material模型</h3>";
try {
    require_once '../app/models/Material.php';
    $materialModel = new Material();
    echo "✅ Material模型实例化成功<br>";
    
    $materials = $materialModel->getAll();
    echo "✅ 获取原材料列表成功，共 " . count($materials) . " 条记录<br>";
    
} catch (Exception $e) {
    echo "❌ Material模型测试失败: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h3>步骤5: 测试控制器</h3>";
try {
    require_once '../app/core/Controller.php';
    require_once '../app/controllers/MaterialController.php';
    
    echo "✅ 控制器文件加载成功<br>";
    
    // 不实例化控制器，避免触发认证
    echo "✅ MaterialController类存在: " . (class_exists('MaterialController') ? '是' : '否') . "<br>";
    
} catch (Exception $e) {
    echo "❌ 控制器测试失败: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h3>步骤6: 手动测试原材料页面</h3>";
echo "所有检查都通过了！现在尝试访问原材料管理页面：<br>";
echo '<a href="index.php?controller=material&action=index" target="_blank">原材料管理</a><br>';

echo "<h3>如果仍然出现重定向循环</h3>";
echo "请检查以下几点：<br>";
echo "1. 清除浏览器缓存和Cookie<br>";
echo "2. 确保会话正常工作<br>";
echo "3. 检查PHP错误日志<br>";

echo "<h3>临时解决方案</h3>";
echo "如果问题持续，可以尝试直接访问：<br>";
echo '<a href="material_simple.php">简化版原材料管理</a><br>';
?>
