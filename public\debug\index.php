<?php
/**
 * 统一调试页面
 * 麻糍工厂销售系统 - 开发调试工具
 * 
 * 功能：
 * - 系统状态检查
 * - 数据库调试
 * - 功能测试
 * - 性能监控
 * - 日志查看
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die('
    <!DOCTYPE html>
    <html>
    <head>
        <title>访问被拒绝</title>
        <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-lock fa-3x text-danger mb-3"></i>
                            <h4>访问被拒绝</h4>
                            <p>此调试页面仅限管理员访问</p>
                            <a href="../index.php" class="btn btn-primary">返回首页</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ');
}

require_once '../../app/core/Database.php';
require_once '../../app/core/SystemHealthChecker.php';

$db = Database::getInstance();
$activeTab = $_GET['tab'] ?? 'overview';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统调试面板 - 麻糍工厂销售系统</title>
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
        }
        .debug-tab {
            border-radius: 10px 10px 0 0;
        }
        .debug-content {
            background: #f8f9fa;
            min-height: 600px;
            padding: 20px;
            border-radius: 0 0 10px 10px;
        }
        .status-card {
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .log-viewer {
            background: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        .test-result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .test-pass { background: #d4edda; color: #155724; }
        .test-fail { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <!-- 调试标识 -->
    <div class="debug-badge">
        <span class="badge bg-danger">DEBUG MODE</span>
    </div>

    <!-- 头部 -->
    <div class="debug-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-bug"></i> 系统调试面板</h2>
                    <p class="mb-0">麻糍工厂销售系统 - 开发调试工具</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group">
                        <a href="../index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-home"></i> 返回系统
                        </a>
                        <button class="btn btn-warning btn-sm" onclick="clearDebugData()">
                            <i class="fas fa-trash"></i> 清理调试数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid mt-4">
        <!-- 导航标签 -->
        <ul class="nav nav-tabs debug-tab" id="debugTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'overview' ? 'active' : ''; ?>" 
                        onclick="switchTab('overview')">
                    <i class="fas fa-tachometer-alt"></i> 系统概览
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'database' ? 'active' : ''; ?>" 
                        onclick="switchTab('database')">
                    <i class="fas fa-database"></i> 数据库调试
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'functions' ? 'active' : ''; ?>" 
                        onclick="switchTab('functions')">
                    <i class="fas fa-cogs"></i> 功能测试
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'performance' ? 'active' : ''; ?>" 
                        onclick="switchTab('performance')">
                    <i class="fas fa-chart-line"></i> 性能监控
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'logs' ? 'active' : ''; ?>" 
                        onclick="switchTab('logs')">
                    <i class="fas fa-file-alt"></i> 日志查看
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo $activeTab === 'tools' ? 'active' : ''; ?>" 
                        onclick="switchTab('tools')">
                    <i class="fas fa-tools"></i> 调试工具
                </button>
            </li>
        </ul>

        <!-- 标签内容 -->
        <div class="debug-content">
            <div id="tab-content">
                <!-- 动态加载内容 -->
                正在加载...
            </div>
        </div>
    </div>

    <script src="../assets/js/bootstrap.min.js"></script>
    <script>
        // 当前活动标签
        let currentTab = '<?php echo $activeTab; ?>';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTabContent(currentTab);
            
            // 每30秒自动刷新当前标签（如果是概览或性能监控）
            setInterval(function() {
                if (currentTab === 'overview' || currentTab === 'performance') {
                    loadTabContent(currentTab);
                }
            }, 30000);
        });
        
        // 切换标签
        function switchTab(tab) {
            // 更新URL
            const url = new URL(window.location);
            url.searchParams.set('tab', tab);
            window.history.pushState({}, '', url);
            
            // 更新活动标签样式
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 加载内容
            currentTab = tab;
            loadTabContent(tab);
        }
        
        // 加载标签内容
        function loadTabContent(tab) {
            const contentDiv = document.getElementById('tab-content');
            contentDiv.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>正在加载...</p></div>';
            
            fetch(`tabs/${tab}.php`)
                .then(response => response.text())
                .then(html => {
                    contentDiv.innerHTML = html;
                })
                .catch(error => {
                    contentDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>加载失败</h5>
                            <p>无法加载标签内容: ${error.message}</p>
                            <button class="btn btn-outline-danger" onclick="loadTabContent('${tab}')">重试</button>
                        </div>
                    `;
                });
        }
        
        // 清理调试数据
        function clearDebugData() {
            if (confirm('确定要清理所有调试数据吗？这将删除所有临时文件和日志。')) {
                fetch('actions/clear_debug.php', {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('调试数据清理完成');
                            location.reload();
                        } else {
                            alert('清理失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('清理失败: ' + error.message);
                    });
            }
        }
        
        // 全局工具函数
        window.debugUtils = {
            // 格式化JSON
            formatJson: function(obj) {
                return JSON.stringify(obj, null, 2);
            },
            
            // 复制到剪贴板
            copyToClipboard: function(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('已复制到剪贴板');
                });
            },
            
            // 下载文件
            downloadFile: function(content, filename) {
                const blob = new Blob([content], {type: 'text/plain'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            }
        };
    </script>
</body>
</html>
