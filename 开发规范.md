# 麻糍工厂销售系统开发规范

## 项目结构
项目采用简化的三层架构：
- **表现层**: Bootstrap、HTML、CSS 和 JavaScript/jQuery
- **应用层/业务逻辑层**: PHP 脚本
- **数据访问层**: PHP 操作 MySQL 数据库

### 目录结构
- `public/`: Web服务器公开访问目录
  - `index.php`: 应用统一入口文件
  - `assets/`: 静态资源
- `app/`: 应用核心代码目录
  - `core/`: 核心类库
    - `Controller.php`: 基础控制器类
    - `Database.php`: 数据库连接处理类
    - `helpers.php`: 通用辅助函数
  - `controllers/`: 控制器
    - `Controller.php`: 仅引入核心控制器类的文件
  - `models/`: 数据模型
  - `views/`: 视图/模板文件
  - `config/`: 配置文件目录

## 开发规范

### 文件结构规范
- 核心类库（如Controller基类）只能在`app/core/`目录中定义
- 不允许在多个位置定义同名类，避免类冲突

### 命名规范
- **控制器**: 大驼峰命名法，如`HomeController`
- **模型**: 单数形式的大驼峰命名法，如`User`
- **视图**: 小写字母和下划线分隔，如`login.php`
- **方法**: 控制器方法采用驼峰式命名，首字母小写，如`index()`

### 引用规范
- 使用`require_once`而非`include`
- 使用相对路径引用文件，如`__DIR__ . '/../core/Controller.php'`
- 所有控制器必须通过以下方式引入基础控制器：
  ```php
  require_once __DIR__ . '/Controller.php';
  ```
- `app/controllers/Controller.php`中只包含：
  ```php
  <?php
  // app/controllers/Controller.php
  // 引入核心控制器类
  require_once __DIR__ . '/../core/Controller.php';
  ```

### 类继承规范
- 所有控制器必须继承自基础控制器`Controller`
- 确保基础控制器只在`app/core/Controller.php`中定义

### 安全规范
- 所有用户输入必须经过验证
- 使用预处理语句防止SQL注入
- 输出到HTML的数据必须经过转义，防止XSS攻击
- 使用`password_hash()`和`password_verify()`处理密码

## 常见问题解决方案
- **类冲突问题**: 确保基础控制器类只在`app/core/Controller.php`中定义
- **路径引用问题**: 使用`__DIR__`确保相对路径正确
- **类继承问题**: 确保基类已被正确引入，检查基类名称拼写是否正确