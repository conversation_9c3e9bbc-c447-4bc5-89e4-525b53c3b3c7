<?php
/**
 * 测试配方创建
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

require_once '../app/core/Database.php';
require_once '../app/models/Recipe.php';

$db = Database::getInstance();

echo "<h2>测试配方创建</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST 数据:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    $productId = $_POST['product_id'] ?? '';
    $recipeName = trim($_POST['recipe_name'] ?? '');
    $materials = $_POST['materials'] ?? [];
    
    echo "<h3>处理结果:</h3>";
    
    // 验证输入
    if (empty($productId)) {
        echo "<p style='color: red;'>❌ 请选择成品</p>";
        exit;
    }

    if (empty($materials)) {
        echo "<p style='color: red;'>❌ 请至少添加一种原材料</p>";
        exit;
    }

    // 验证原材料数据
    $recipeItems = [];
    foreach ($materials as $material) {
        if (empty($material['material_id']) || empty($material['quantity']) || empty($material['unit'])) {
            echo "<p style='color: red;'>❌ 请完整填写所有原材料信息</p>";
            exit;
        }

        $recipeItems[] = [
            'material_id' => $material['material_id'],
            'quantity_needed' => floatval($material['quantity']),
            'unit_of_material' => $material['unit']
        ];
    }

    echo "<p style='color: green;'>✅ 数据验证通过</p>";
    echo "<h4>处理后的数据:</h4>";
    echo "<pre>";
    print_r($recipeItems);
    echo "</pre>";

    try {
        $recipeModel = new Recipe();
        $recipeId = $recipeModel->create($productId, $recipeName, $recipeItems);
        
        echo "<p style='color: green;'>✅ 配方创建成功！ID: $recipeId</p>";
        
        // 验证创建结果
        $createdRecipe = $recipeModel->getWithItems($recipeId);
        echo "<h4>创建的配方:</h4>";
        echo "<pre>";
        print_r($createdRecipe);
        echo "</pre>";
        
        echo "<p><a href='debug.php?action=recipes'>查看配方调试</a></p>";
        echo "<p><a href='index.php?controller=recipe&action=index'>查看配方管理</a></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 配方创建失败: " . $e->getMessage() . "</p>";
        echo "<p>错误详情: " . $e->getTraceAsString() . "</p>";
    }
    
} else {
    // 显示表单
    $products = $db->fetchAll("SELECT * FROM products ORDER BY name");
    $materials = $db->fetchAll("SELECT * FROM materials ORDER BY name");
    ?>
    
    <form method="POST">
        <h3>创建配方</h3>
        
        <div style="margin: 10px 0;">
            <label>选择成品:</label>
            <select name="product_id" required>
                <option value="">请选择成品</option>
                <?php foreach ($products as $product): ?>
                    <option value="<?php echo $product['id']; ?>">
                        <?php echo htmlspecialchars($product['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div style="margin: 10px 0;">
            <label>配方名称:</label>
            <input type="text" name="recipe_name" placeholder="可选">
        </div>
        
        <h4>原材料:</h4>
        
        <div style="margin: 10px 0;">
            <label>原材料1:</label>
            <select name="materials[0][material_id]" required>
                <option value="">请选择原材料</option>
                <?php foreach ($materials as $material): ?>
                    <option value="<?php echo $material['id']; ?>">
                        <?php echo htmlspecialchars($material['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <input type="number" name="materials[0][quantity]" placeholder="数量" step="0.1" required>
            <input type="text" name="materials[0][unit]" value="g" readonly>
        </div>
        
        <div style="margin: 10px 0;">
            <label>原材料2:</label>
            <select name="materials[1][material_id]">
                <option value="">请选择原材料</option>
                <?php foreach ($materials as $material): ?>
                    <option value="<?php echo $material['id']; ?>">
                        <?php echo htmlspecialchars($material['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <input type="number" name="materials[1][quantity]" placeholder="数量" step="0.1">
            <input type="text" name="materials[1][unit]" value="g" readonly>
        </div>
        
        <div style="margin: 20px 0;">
            <button type="submit">创建配方</button>
        </div>
    </form>
    
    <p><a href="debug.php">返回调试页面</a></p>
    
    <?php
}
?>
