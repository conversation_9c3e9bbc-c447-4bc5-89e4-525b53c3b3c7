<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 欢迎信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title mb-1">
                    <i class="fas fa-home text-primary"></i> 
                    欢迎回来，<?php echo htmlspecialchars($_SESSION['name'] ?? $_SESSION['username']); ?>！
                </h4>
                <p class="card-text text-muted">
                    今天是 <?php echo date('Y年m月d日 星期' . ['日','一','二','三','四','五','六'][date('w')]); ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['products_count']; ?></div>
            <div class="stat-label">成品种类</div>
            <div class="mt-2">
                <a href="index.php?controller=product&action=index" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i> 查看
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['materials_count']; ?></div>
            <div class="stat-label">原材料种类</div>
            <div class="mt-2">
                <a href="index.php?controller=material&action=index" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i> 查看
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['customers_count']; ?></div>
            <div class="stat-label">客户数量</div>
            <div class="mt-2">
                <a href="index.php?controller=customer&action=index" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i> 查看
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number text-warning">¥<?php echo number_format($stats['total_debt'], 2); ?></div>
            <div class="stat-label">总欠款</div>
            <div class="mt-2">
                <a href="index.php?controller=customer&action=index" class="btn btn-sm btn-outline-warning">
                    <i class="fas fa-eye"></i> 查看
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="quick-entry">
            <h5><i class="fas fa-bolt"></i> 快速操作</h5>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-2">
                    <a href="index.php?controller=sale&action=create" class="btn btn-light w-100">
                        <i class="fas fa-plus-circle"></i> 快速开单
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-2">
                    <a href="index.php?controller=product&action=create" class="btn btn-light w-100">
                        <i class="fas fa-box"></i> 添加成品
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-2">
                    <a href="index.php?controller=material&action=create" class="btn btn-light w-100">
                        <i class="fas fa-cubes"></i> 添加原材料
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-2">
                    <a href="index.php?controller=inventory&action=adjust" class="btn btn-light w-100">
                        <i class="fas fa-edit"></i> 库存调整
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 库存警告 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 库存警告
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($lowStockItems)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <p>库存充足，无需警告</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>类型</th>
                                    <th>库存</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockItems as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $item['type'] === 'product' ? 'bg-primary' : 'bg-secondary'; ?>">
                                                <?php echo $item['type'] === 'product' ? '成品' : '原材料'; ?>
                                            </span>
                                        </td>
                                        <td class="text-warning">
                                            <?php echo $item['stock_quantity']; ?> <?php echo htmlspecialchars($item['unit']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="index.php?controller=inventory&action=index" class="btn btn-sm btn-warning">
                            查看详细库存
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 最近销售 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line text-success"></i> 最近销售
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentSales)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <p>暂无销售记录</p>
                        <a href="index.php?controller=sale&action=create" class="btn btn-sm btn-success">
                            立即开单
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>单号</th>
                                    <th>客户</th>
                                    <th>金额</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentSales as $sale): ?>
                                    <tr>
                                        <td>
                                            <a href="index.php?controller=sale&action=detail&id=<?php echo $sale['id']; ?>" class="text-decoration-none">
                                                #<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($sale['customer_name'] ?? '散客'); ?></td>
                                        <td>¥<?php echo number_format($sale['total_amount'], 2); ?></td>
                                        <td><?php echo date('m-d H:i', strtotime($sale['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="index.php?controller=sale&action=index" class="btn btn-sm btn-success">
                            查看全部销售记录
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle text-info"></i> 系统信息
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <small class="text-muted">系统版本:</small><br>
                        <span>v1.0.0</span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">PHP版本:</small><br>
                        <span><?php echo PHP_VERSION; ?></span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">当前时间:</small><br>
                        <span><?php echo date('Y-m-d H:i:s'); ?></span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">登录时间:</small><br>
                        <span><?php echo date('H:i:s'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
