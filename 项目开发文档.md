npx @agentdeskai/browser-tools-server@1.2.0

# 麻糍工厂销售系统 - 项目开发文档

## 1. 项目概述

### 1.1 项目名称
麻糍工厂销售系统

### 1.2 项目目标
开发一套满足以下需求的麻糍工厂销售系统：
*   用户登录认证。
*   管理成品和原材料库存，允许负库存。
*   成品与原材料之间通过配方关联。
*   销售成品后，能（大致）自动计算耗材使用量并扣减相应库存，允许手动库存调整。
*   支持客户欠款管理，清晰计算客户往来账目。
*   原料采购为现结算，可简化处理。
*   支持快速录单（销售订单品类少，主要输入数量）。
*   应用需方便在手机端使用，界面扁平化、卡片式设计，力求美观。
*   系统供少数内部人员（不超过5人）使用，功能不宜臃肿复杂。

### 1.3 技术栈
*   后端：PHP
*   数据库：MySQL
*   前端：Bootstrap (结合HTML, CSS, JavaScript/jQuery)

### 1.4 目标用户
内部员工，人数不超过5人。

## 2. 项目架构

### 2.1 总体架构
采用简化的三层架构思想：
*   **表现层 (Presentation Layer)**：由 Bootstrap、HTML、CSS 和 JavaScript/jQuery 构成，负责用户界面的展示和用户交互。
*   **应用层/业务逻辑层 (Application/Business Logic Layer)**：由 PHP 脚本构成，负责处理业务规则、用户请求、数据处理和逻辑运算。
*   **数据访问层 (Data Access Layer)**：通过 PHP 操作 MySQL 数据库，负责数据的持久化存储和检索。

### 2.2 文件/目录结构
```
/麻糍工厂销售系统/
├── public/                   # Web服务器公开访问目录 (Web Root)
│   ├── index.php             # 应用统一入口文件 (前端控制器)
│   ├── assets/               # 静态资源 (CSS, JS, Images)
│   │   ├── css/              # 自定义CSS文件, Bootstrap CSS
│   │   │   ├── bootstrap.min.css
│   │   │   └── style.css
│   │   ├── js/               # 自定义JS文件, Bootstrap JS, jQuery
│   │   │   ├── jquery.min.js
│   │   │   ├── bootstrap.min.js
│   │   │   └── app.js
│   │   └── images/           # 图片资源
│   └── .htaccess             # (可选) Apache URL重写规则，用于实现友好URL
├── app/                      # 应用核心代码目录
│   ├── core/                 # 核心类库 (如数据库连接, 基础控制器, 辅助函数)
│   │   ├── Database.php      # 数据库连接处理类
│   │   ├── Controller.php    # 基础控制器类 (可选)
│   │   └── helpers.php       # 通用辅助函数
│   ├── controllers/          # 控制器 (处理用户请求, 调用模型, 加载视图)
│   │   ├── AuthController.php
│   │   ├── ProductController.php
│   │   ├── MaterialController.php
│   │   ├── InventoryController.php
│   │   ├── SaleController.php
│   │   └── CustomerController.php
│   ├── models/               # 数据模型 (与数据库表对应, 封装数据操作逻辑)
│   │   ├── User.php
│   │   ├── Product.php         # 成品模型
│   │   ├── Material.php        # 原材料模型
│   │   ├── Recipe.php          # 配方模型
│   │   ├── Sale.php            # 销售订单模型
│   │   ├── Customer.php        # 客户模型
│   │   └── CustomerLedger.php  # 客户账目流水模型
│   ├── views/                # 视图/模板文件 (HTML页面片段)
│   │   ├── layouts/          # 布局模板 (如页眉, 页脚, 导航栏)
│   │   │   ├── header.php
│   │   │   ├── footer.php
│   │   │   └── navigation.php
│   │   ├── auth/             # 认证相关视图 (登录页面)
│   │   │   └── login.php
│   │   ├── products/         # 成品管理相关视图
│   │   ├── materials/        # 原材料管理相关视图
│   │   ├── inventory/        # 库存管理相关视图
│   │   ├── sales/            # 销售管理相关视图
│   │   └── customers/        # 客户管理相关视图
│   ├── config/               # 配置文件目录
│   │   └── database.php      # 数据库连接配置
│   └── routes.php            # (可选) 路由定义文件，如果使用简单路由分发
├── vendor/                   # (可选) Composer 管理的第三方依赖库 (如将Bootstrap通过Composer引入)
└── README.md                 # 项目说明文档
```
*说明：此结构为建议，可根据实际开发方式（如是否使用PHP框架）进行调整。若不使用框架，`index.php` 将承担更多的路由和请求分发工作。*

### 2.3 数据库设计 (核心表)
基于您简单化的需求，核心表初步设计如下 (详细字段待后续细化)：
*   `users` (用户表): `id`, `username`, `password_hash`, `name`, `created_at`
*   `materials` (原材料表): `id`, `name`, `specification` (规格, 如每袋500g), `unit` (单位, 如袋/g), `stock_quantity` (库存数量), `unit_price` (单价，用于成本计算)
*   `products` (成品表): `id`, `name`, `specification`, `unit`, `stock_quantity`, `selling_price` (销售单价)
*   `recipes` (配方表): `id`, `product_id` (关联成品), `name` (配方名称, 可选)
*   `recipe_items` (配方明细表): `id`, `recipe_id`, `material_id` (关联原材料), `quantity_needed` (所需数量), `unit_of_material` (原材料单位，如g)
*   `customers` (客户表): `id`, `name`, `contact_info`, `balance` (当前欠款)
*   `sales_orders` (销售订单表): `id`, `customer_id`, `order_date`, `total_amount`, `amount_paid`, `status` (如待付款, 部分付款, 已付清)
*   `sales_order_items` (销售订单明细表): `id`, `sales_order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`
*   `inventory_transactions` (库存流水表): `id`, `item_id` (关联成品或原材料ID), `item_type` ('product' 或 'material'), `transaction_type` ('sale', 'purchase', 'adjustment_in', 'adjustment_out', 'production_consumption'), `quantity_change`, `transaction_date`, `notes`
*   `customer_ledger_entries` (客户账目流水表): `id`, `customer_id`, `entry_date`, `description` (如销售单号XXX, 收款), `debit_amount` (借方金额/应收增加), `credit_amount` (贷方金额/应收减少), `balance_after_entry`

*供应商表可以极度简化，甚至初期不单独建表，采购时直接记录原材料入库和成本即可。*

### 2.4 关键组件与技术点
*   **PHP**:
    *   使用原生PHP，结合面向过程和部分面向对象的编程方式。
    *   通过包含文件 (`include`, `require`) 组织代码。
    *   表单数据处理：使用 `$_POST`, `$_GET`。
    *   数据库操作：使用 `PDO` 或 `mysqli` 扩展，务必使用预处理语句 (Prepared Statements) 防止SQL注入。
*   **MySQL**:
    *   关系型数据存储。
    *   设计合理的索引以优化查询性能。
*   **Bootstrap**:
    *   使用其栅格系统 (Grid System) 进行页面响应式布局。
    *   利用预定义的CSS类快速构建表单、按钮、导航、卡片等UI元素。
    *   使用其JavaScript插件实现模态框、下拉菜单等交互效果。
*   **jQuery**:
    *   Bootstrap的JavaScript插件依赖jQuery。
    *   可用于简化DOM操作和AJAX异步请求（例如，在不刷新页面的情况下提交表单或更新数据）。
*   **会话管理 (Session)**:
    *   PHP内置的 `$_SESSION` 机制用于管理用户登录状态。
*   **安全性考量**:
    *   **输入验证**: 对所有用户输入进行严格验证（前端和后端双重验证）。
    *   **SQL注入防护**: 必须使用参数化查询或预处理语句。
    *   **XSS防护**: 对输出到HTML的数据进行转义 (如使用 `htmlspecialchars()`)。
    *   **密码安全**: 存储密码时使用强哈希算法 (如 `password_hash()` 和 `password_verify()`)。
*   **错误处理与日志**:
    *   设置合理的错误报告级别。
    *   考虑记录关键操作日志或错误日志。

## 3. 开发流程与进度

以下是一个建议的开发流程和大致的阶段划分，具体时间需根据实际情况调整。

**阶段一：准备与设计 (预计 3-5 天)**
*   [ ] **需求细化与确认**: 与您再次沟通，确保所有核心需求理解一致。
*   [ ] **数据库详细设计**: 完成所有表的字段、类型、约束、关系的设计，并创建ER图。
*   [ ] **原型设计 (可选但推荐)**: 使用纸笔或简单工具绘制主要界面的草图，明确用户操作流程。
*   [ ] **开发环境搭建**: 确保PHPStudy Pro环境配置无误，PHP、MySQL运行正常，创建项目目录结构。
*   [ ] **版本控制**: 初始化Git仓库。

**阶段二：基础框架与用户认证 (预计 5-7 天)**
*   [ ] **数据库连接**: 实现 `app/core/Database.php`，提供统一的数据库连接方法。
*   [ ] **基础布局与样式**: 使用Bootstrap搭建网站基本布局 (`header.php`, `footer.php`, `navigation.php`) 和引入基础CSS。
*   [ ] **用户模型与认证**:
    *   [ ] 创建 `users` 表。
    *   [ ] 实现 `AuthController.php` 和 `User.php` 模型。
    *   [ ] 开发用户登录页面 (`views/auth/login.php`) 及后端登录逻辑（含密码验证、Session处理）。
    *   [ ] 实现访问控制，未登录用户跳转到登录页。
    *   [ ] 开发登出功能。

**阶段三：核心模块 - 原材料、成品与配方管理 (预计 7-10 天)**
*   [ ] **原材料管理**:
    *   [ ] 创建 `materials` 表。
    *   [ ] 开发 `MaterialController.php` 和 `Material.php` 模型。
    *   [ ] 实现原材料的增、删、改、查界面及后端逻辑。
*   [ ] **成品管理**:
    *   [ ] 创建 `products` 表。
    *   [ ] 开发 `ProductController.php` 和 `Product.php` 模型。
    *   [ ] 实现成品的增、删、改、查界面及后端逻辑。
*   [ ] **配方管理**:
    *   [ ] 创建 `recipes` 和 `recipe_items` 表。
    *   [ ] 开发配方管理相关控制器和模型。
    *   [ ] 实现配方的创建、查看、修改功能（关联成品和原材料）。

**阶段四：核心业务流程 - 销售与库存 (预计 10-15 天)**
*   [ ] **销售管理**:
    *   [ ] 创建 `customers`, `sales_orders`, `sales_order_items`, `customer_ledger_entries` 表。
    *   [ ] 开发 `SaleController.php`, `CustomerController.php` 及相关模型。
    *   [ ] 实现销售开单界面：选择客户、选择成品、输入数量（考虑快速录单需求）。
    *   [ ] 后端逻辑：
        *   保存销售订单及明细。
        *   根据配方扣减原材料库存（更新`materials.stock_quantity`，记录`inventory_transactions`）。
        *   扣减成品库存（更新`products.stock_quantity`，记录`inventory_transactions`）。
        *   更新客户欠款（`customers.balance`，记录`customer_ledger_entries`）。
    *   [ ] 实现销售订单查看功能。
*   [ ] **库存管理**:
    *   [ ] 创建 `inventory_transactions` 表 (如果阶段三未完全创建)。
    *   [ ] 开发 `InventoryController.php`。
    *   [ ] 实现库存查询界面（原材料和成品库存列表）。
    *   [ ] 实现库存调整功能（手动增减库存，并记录流水）。
*   [ ] **客户账目管理**:
    *   [ ] 实现客户列表及欠款查看。
    *   [ ] 实现客户往来账目流水查看。
    *   [ ] (可选) 简单的客户收款功能。

**阶段五：完善、测试与优化 (预计 7-10 天)**
*   [ ] **界面优化**: 根据Bootstrap特性，优化所有页面的移动端显示效果和用户体验。
*   [ ] **功能完善**: 检查所有核心流程，补充遗漏的细节功能。
*   [ ] **输入验证强化**: 对所有用户输入进行严格的前后端验证。
*   [ ] **错误处理**: 完善错误提示和异常处理。
*   [ ] **单元测试**: 对关键的业务逻辑函数/方法编写简单的测试。
*   [ ] **集成测试**: 全面测试系统各项功能和流程，记录并修复Bug。
*   [ ] **性能考量**: 对于数据量可能增长的查询，检查是否需要优化或添加索引。
*   [ ] **编写用户操作手册**: 简单明了的操作指南。

**阶段六：部署与交付 (预计 1-2 天)**
*   [ ] **最终测试**: 在类生产环境中进行最后测试。
*   [ ] **部署**: 将代码部署到用户指定的PHPStudy环境中。
*   [ ] **数据初始化**: 如有必要，导入初始数据。
*   [ ] **用户培训**: 对用户进行系统操作培训。
*   [ ] **收集反馈**: 收集初步使用反馈，进行微调。

**总计预估时间：约 33 - 59 工作日 (这是一个非常粗略的估计，实际会因具体细节和开发效率而变化)**

## 4. 风险与应对

*   **需求理解偏差或变更**:
    *   **应对**: 项目初期加强沟通，详细记录需求。开发过程中保持与用户的沟通渠道畅通，对于小的变更灵活处理，大的变更评估影响并与用户协商。
*   **技术实现难点**:
    *   **应对**: 提前研究关键技术点，遇到问题时积极搜索解决方案、查阅文档或寻求外部帮助。
*   **开发时间预估不足**:
    *   **应对**: 将任务细化，定期回顾进度，如果发现偏差及时调整计划或与用户沟通。
*   **数据准确性问题 (特别是自动扣减库存)**:
    *   **应对**: 仔细设计相关逻辑，充分测试各种边界条件。提供库存调整功能作为修正手段。
*   **安全性漏洞**:
    *   **应对**: 遵循安全编码规范，特别是SQL注入、XSS等常见漏洞的防护。

## 5. 附录 (可选)

*   API接口文档 (如果未来考虑前后端分离或提供API给其他系统)
*   数据库ER图

## 6. 部署与环境配置

### 6.1 宝塔面板配置

本项目使用宝塔面板进行部署和管理，具体配置如下：

* **网站根目录**：`test-cr`（项目根目录）
* **网站运行目录**：`public`（项目中的public文件夹）
* **访问方式**：所有Web请求都通过public目录处理，直接访问项目根目录下的文件将无法通过浏览器访问

### 6.2 部署注意事项

由于运行目录设置为public，在开发和部署过程中需要注意以下几点：

1. **文件访问路径**：
   * 只有放在public目录下的文件可以通过浏览器直接访问
   * 项目根目录下的文件（如创建用户脚本等）需要复制到public目录才能通过浏览器访问
   * 所有资源文件（CSS、JS、图片等）必须放在public目录下

2. **路径引用**：
   * 在public目录下的文件引用项目其他目录的文件时，需使用相对路径：`__DIR__ . '/../app/...`
   * 在控制器、模型等文件中引用其他文件时，使用相对于项目根目录的路径

3. **URL设计**：
   * 所有URL都应该指向public目录下的文件
   * 主入口文件为public/index.php
   * 访问URL示例：`http://域名/index.php?controller=auth&action=login`

4. **安全性**：
   * 敏感配置文件和业务逻辑文件放在app目录下，不在public目录中，提高安全性
   * 数据库连接信息等敏感信息不应该放在可公开访问的文件中

5. **调试和维护工具**：
   * 需要通过浏览器访问的调试和维护工具（如创建管理员账号）应放在public目录下
   * 这些工具应添加适当的访问控制，避免未授权访问

## 7. 项目开发规范

为确保项目开发的一致性和可维护性，避免类似"Class 'Controller' not found"等错误，特制定以下开发规范。所有开发人员必须严格遵守。

### 7.1 文件结构规范

按照项目目录结构严格组织文件：
- `app/core/` 存放核心类库，包括基础控制器、数据库连接和辅助函数
- `app/controllers/` 存放各个功能模块的控制器
- `app/models/` 存放数据模型
- `app/views/` 存放视图文件
- `public/` 作为网站根目录，存放入口文件和静态资源

**重要规定**：
- 核心类库（如Controller基类）只能在`app/core/`目录中定义
- 不允许在多个位置定义同名类，避免类冲突

### 7.2 命名规范

#### 7.2.1 控制器命名
- 控制器类名采用大驼峰命名法，如`HomeController`、`CustomerController`
- 控制器文件名与类名一致，如`HomeController.php`
- 基础控制器类只能在`app/core/Controller.php`中定义
- 特殊情况：`app/controllers/Controller.php`仅作为引入文件，不定义类

#### 7.2.2 模型命名
- 模型类名采用单数形式的大驼峰命名法，如`User`、`Product`
- 模型文件名与类名一致，如`User.php`

#### 7.2.3 视图命名
- 视图文件采用小写字母和下划线分隔，如`login.php`、`customer_list.php`
- 视图文件按功能模块组织在对应目录下

#### 7.2.4 方法命名
- 控制器方法采用驼峰式命名，首字母小写，如`index()`、`createUser()`
- 模型方法采用描述性名称，如`getById()`、`create()`、`update()`

### 7.3 引用规范

#### 7.3.1 文件引用
- 使用`require_once`而非`include`，避免重复引入
- 使用相对路径引用文件，如`__DIR__ . '/../core/Controller.php'`
- 控制器必须先引入基础控制器，再引入所需的模型

#### 7.3.2 标准引用方式
- 所有控制器必须通过以下方式引入基础控制器：
  ```php
  require_once __DIR__ . '/Controller.php';
  ```
- `app/controllers/Controller.php`中只包含：
  ```php
  <?php
  // app/controllers/Controller.php
  // 引入核心控制器类
  require_once __DIR__ . '/../core/Controller.php';
  ```

#### 7.3.3 类继承
- 所有控制器必须继承自基础控制器`Controller`
- 确保基础控制器只在`app/core/Controller.php`中定义

### 7.4 代码组织规范

#### 7.4.1 控制器组织
- 每个控制器负责一个功能模块
- 控制器应遵循单一职责原则，不要将不相关的功能混在一起
- 控制器中应避免直接操作数据库，而是通过模型进行

#### 7.4.2 模型组织
- 模型负责与数据库交互的逻辑
- 模型方法应清晰表明其功能
- 复杂的业务逻辑应在模型中实现，而非控制器

#### 7.4.3 视图组织
- 视图只负责展示，不包含业务逻辑
- 视图应通过控制器传入数据进行渲染
- 视图中避免直接操作数据库或执行复杂计算

### 7.5 安全规范

#### 7.5.1 输入验证
- 所有用户输入必须经过验证
- 使用预处理语句防止SQL注入
- 验证应同时在前端和后端进行

#### 7.5.2 输出转义
- 输出到HTML的数据必须经过转义，防止XSS攻击
- 使用`htmlspecialchars()`函数处理输出

#### 7.5.3 密码安全
- 使用`password_hash()`和`password_verify()`处理密码
- 不允许明文存储密码

### 7.6 避免冲突的具体措施

#### 7.6.1 统一基础类
- 基础控制器类`Controller`只在`app/core/Controller.php`中定义
- 在`app/controllers`目录下不再创建同名类文件，只创建引入文件

#### 7.6.2 统一引入方式
- 所有控制器统一使用`require_once __DIR__ . '/Controller.php'`引入基础控制器
- `app/controllers/Controller.php`仅作为中转，引入核心控制器

#### 7.6.3 代码审查机制
- 在提交代码前进行自检，确保没有重复定义的类
- 检查文件引用路径是否正确
- 定期进行代码审查，确保规范的遵守

### 7.7 常见问题及解决方案

#### 7.7.1 类冲突问题
- **问题**：出现"Class 'Controller' not found"等类似错误
- **原因**：可能是多个地方定义了同名类，或引用路径错误
- **解决方案**：
  1. 检查是否在多个位置定义了同名类
  2. 确保基础控制器类只在`app/core/Controller.php`中定义
  3. 检查引用路径是否正确

#### 7.7.2 路径引用问题
- **问题**：文件找不到或引用错误
- **解决方案**：
  1. 使用`__DIR__`确保相对路径正确
  2. 检查文件是否存在于指定位置
  3. 确保路径大小写正确（在Linux系统上尤为重要）

#### 7.7.3 类继承问题
- **问题**：无法正确继承基类
- **解决方案**：
  1. 确保基类已被正确引入
  2. 检查基类名称拼写是否正确
  3. 确保没有类名冲突

通过严格遵循这些规范，可以避免类似的命名冲突和文件结构混乱问题，确保项目开发的一致性和可维护性。

---

本文档为初步规划，具体细节将在开发过程中逐步完善和调整。 