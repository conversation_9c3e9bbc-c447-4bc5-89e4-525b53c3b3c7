<?php
/**
 * 测试数据库连接
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '权限不足']);
    exit;
}

require_once '../../../app/core/Database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $startTime = microtime(true);
    
    // 测试基本连接
    $result = $db->fetch("SELECT 1 as test");
    $connectionTime = (microtime(true) - $startTime) * 1000;
    
    // 测试表查询
    $tables = [];
    $tableList = $db->fetchAll("SHOW TABLES");
    foreach ($tableList as $table) {
        $tableName = array_values($table)[0];
        $count = $db->fetch("SELECT COUNT(*) as count FROM `$tableName`");
        $tables[$tableName] = $count['count'];
    }
    
    // 测试写入权限（创建临时表）
    $writeTest = false;
    try {
        $db->execute("CREATE TEMPORARY TABLE debug_test (id INT)");
        $db->execute("INSERT INTO debug_test VALUES (1)");
        $testResult = $db->fetch("SELECT * FROM debug_test");
        $writeTest = $testResult['id'] == 1;
    } catch (Exception $e) {
        // 写入测试失败
    }
    
    // 获取数据库信息
    $dbInfo = [];
    try {
        $version = $db->fetch("SELECT VERSION() as version");
        $dbInfo['version'] = $version['version'];
        
        $charset = $db->fetch("SELECT @@character_set_database as charset");
        $dbInfo['charset'] = $charset['charset'];
        
        $collation = $db->fetch("SELECT @@collation_database as collation");
        $dbInfo['collation'] = $collation['collation'];
    } catch (Exception $e) {
        // 忽略错误
    }
    
    echo json_encode([
        'success' => true,
        'message' => '数据库连接测试成功',
        'details' => [
            'connection_time' => round($connectionTime, 2) . 'ms',
            'tables' => $tables,
            'table_count' => count($tables),
            'total_records' => array_sum($tables),
            'write_permission' => $writeTest,
            'database_info' => $dbInfo
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败: ' . $e->getMessage(),
        'details' => [
            'error_code' => $e->getCode(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine()
        ]
    ]);
}
?>
