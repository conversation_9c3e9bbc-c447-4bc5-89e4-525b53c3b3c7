<?php
/**
 * 基础控制器类
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/Database.php';

class Controller
{
    protected $db;

    public function __construct()
    {
        // 启动会话
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        // 获取数据库实例
        $this->db = Database::getInstance();
    }

    /**
     * 加载视图文件
     */
    protected function view($viewPath, $data = [])
    {
        // 将数据提取为变量
        extract($data);
        
        // 构建视图文件路径
        $viewFile = __DIR__ . '/../views/' . $viewPath . '.php';
        
        if (file_exists($viewFile)) {
            require $viewFile;
        } else {
            die("视图文件不存在: {$viewPath}");
        }
    }

    /**
     * 重定向到指定URL
     */
    protected function redirect($url)
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * 检查用户是否已登录
     */
    protected function requireAuth()
    {
        if (!$this->isLoggedIn()) {
            $this->redirect('index.php?controller=auth&action=login');
        }
    }

    /**
     * 检查用户登录状态
     */
    protected function isLoggedIn()
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * 获取当前登录用户信息
     */
    protected function getCurrentUser()
    {
        if ($this->isLoggedIn()) {
            return [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'] ?? '',
                'name' => $_SESSION['name'] ?? ''
            ];
        }
        return null;
    }

    /**
     * 设置Flash消息
     */
    protected function setFlash($type, $message)
    {
        $_SESSION['flash'][$type] = $message;
    }

    /**
     * 获取Flash消息
     */
    protected function getFlash($type)
    {
        if (isset($_SESSION['flash'][$type])) {
            $message = $_SESSION['flash'][$type];
            unset($_SESSION['flash'][$type]);
            return $message;
        }
        return null;
    }

    /**
     * 验证CSRF Token（简化版）
     */
    protected function validateCsrfToken()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? '';
            $sessionToken = $_SESSION['csrf_token'] ?? '';
            
            if (empty($token) || $token !== $sessionToken) {
                die('CSRF验证失败');
            }
        }
    }

    /**
     * 生成CSRF Token
     */
    protected function generateCsrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * 输出JSON响应
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 验证输入数据
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            
            // 必填验证
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = $rule['message'] ?? "{$field}不能为空";
                continue;
            }
            
            // 长度验证
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field] = $rule['message'] ?? "{$field}长度不能少于{$rule['min_length']}个字符";
            }
            
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field] = $rule['message'] ?? "{$field}长度不能超过{$rule['max_length']}个字符";
            }
            
            // 邮箱验证
            if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = $rule['message'] ?? "{$field}格式不正确";
            }
        }
        
        return $errors;
    }
}
