<?php
/**
 * 调试配方表单提交
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

echo "<h2>配方表单调试</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST 数据:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>分析:</h3>";
    
    $productId = $_POST['product_id'] ?? '';
    $recipeName = trim($_POST['recipe_name'] ?? '');
    $materials = $_POST['materials'] ?? [];
    
    echo "<p>Product ID: " . ($productId ? $productId : '空') . "</p>";
    echo "<p>Recipe Name: " . ($recipeName ? $recipeName : '空') . "</p>";
    echo "<p>Materials 数组:</p>";
    
    if (empty($materials)) {
        echo "<p style='color: red;'>❌ Materials 数组为空！</p>";
    } else {
        echo "<p style='color: green;'>✅ Materials 数组有数据</p>";
        foreach ($materials as $index => $material) {
            echo "<p>Material $index:</p>";
            echo "<ul>";
            echo "<li>material_id: " . ($material['material_id'] ?? '空') . "</li>";
            echo "<li>quantity: " . ($material['quantity'] ?? '空') . "</li>";
            echo "<li>unit: " . ($material['unit'] ?? '空') . "</li>";
            echo "</ul>";
        }
    }
    
    // 验证原材料数据
    $recipeItems = [];
    $hasError = false;
    foreach ($materials as $material) {
        if (empty($material['material_id']) || empty($material['quantity']) || empty($material['unit'])) {
            echo "<p style='color: red;'>❌ 原材料数据不完整</p>";
            $hasError = true;
            break;
        }

        $recipeItems[] = [
            'material_id' => $material['material_id'],
            'quantity_needed' => floatval($material['quantity']),
            'unit_of_material' => $material['unit']
        ];
    }
    
    if (!$hasError) {
        echo "<p style='color: green;'>✅ 原材料数据验证通过</p>";
        echo "<h4>处理后的数据:</h4>";
        echo "<pre>";
        print_r($recipeItems);
        echo "</pre>";
        
        // 尝试创建配方
        try {
            require_once '../app/core/Database.php';
            require_once '../app/models/Recipe.php';
            
            $recipeModel = new Recipe();
            $recipeId = $recipeModel->create($productId, $recipeName, $recipeItems);
            
            echo "<p style='color: green;'>✅ 配方创建成功！ID: $recipeId</p>";
            echo "<a href='debug_recipes.php'>查看配方数据</a>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 配方创建失败: " . $e->getMessage() . "</p>";
        }
    }
    
} else {
    echo "<p>请通过配方创建页面提交表单来调试</p>";
}

echo "<br><br>";
echo "<a href='index.php?controller=recipe&action=create'>返回配方创建页面</a><br>";
echo "<a href='debug_recipes.php'>查看配方数据</a>";
?>
