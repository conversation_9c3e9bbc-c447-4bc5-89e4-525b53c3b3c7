<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题和操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-list-ul text-primary"></i> 配方管理</h2>
        <p class="text-muted mb-0">管理成品的生产配方和原材料配比</p>
    </div>
    <div>
        <a href="index.php?controller=recipe&action=create" class="btn btn-primary">
            <i class="fas fa-plus"></i> 创建配方
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_recipes']; ?></div>
            <div class="stat-label">总配方数</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stat-card">
            <div class="stat-number text-success"><?php echo $stats['products_with_recipe']; ?></div>
            <div class="stat-label">已配置配方</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stat-card">
            <div class="stat-number text-warning"><?php echo $stats['products_without_recipe']; ?></div>
            <div class="stat-label">未配置配方</div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="controller" value="recipe">
            <input type="hidden" name="action" value="index">
            
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="搜索成品名称或配方名称..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <?php if (!empty($search)): ?>
                    <a href="index.php?controller=recipe&action=index" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 清除搜索
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- 配方列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> 配方列表
            <?php if (!empty($search)): ?>
                <small class="text-muted">- 搜索结果: "<?php echo htmlspecialchars($search); ?>"</small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($recipes)): ?>
            <div class="text-center py-5">
                <i class="fas fa-list-ul fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    <?php echo !empty($search) ? '未找到匹配的配方' : '暂无配方'; ?>
                </h5>
                <p class="text-muted">
                    <?php echo !empty($search) ? '请尝试其他搜索关键词' : '点击上方按钮创建第一个配方'; ?>
                </p>
                <?php if (empty($search)): ?>
                    <a href="index.php?controller=recipe&action=create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 创建配方
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>成品名称</th>
                            <th>配方名称</th>
                            <th>成品单位</th>
                            <th>原材料数量</th>
                            <th>创建时间</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recipes as $recipe): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($recipe['product_name']); ?></strong>
                                </td>
                                <td>
                                    <?php if (!empty($recipe['name'])): ?>
                                        <?php echo htmlspecialchars($recipe['name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">默认配方</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($recipe['product_unit']); ?></td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php 
                                        // 这里需要查询配方项目数量，暂时显示为 "-"
                                        echo '-'; 
                                        ?> 种原材料
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($recipe['created_at'])); ?></td>
                                <td>
                                    <?php if ($recipe['updated_at'] !== $recipe['created_at']): ?>
                                        <?php echo date('Y-m-d', strtotime($recipe['updated_at'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">未更新</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="index.php?controller=recipe&action=detail&id=<?php echo $recipe['id']; ?>" 
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?controller=recipe&action=edit&id=<?php echo $recipe['id']; ?>" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?controller=recipe&action=delete&id=<?php echo $recipe['id']; ?>" 
                                           class="btn btn-outline-danger btn-delete" 
                                           data-item-name="<?php echo htmlspecialchars($recipe['product_name']); ?>的配方"
                                           title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 快速操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 配方管理提示
                </h6>
                <ul class="mb-0">
                    <li>每个成品只能有一个配方</li>
                    <li>配方定义了生产1个单位成品所需的原材料</li>
                    <li>删除配方不会影响成品信息</li>
                    <li>配方用于自动计算生产成本和库存扣减</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-plus-circle text-success"></i> 快速操作
                </h6>
                <div class="d-grid gap-2">
                    <a href="index.php?controller=product&action=index" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-box"></i> 管理成品
                    </a>
                    <a href="index.php?controller=material&action=index" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-cubes"></i> 管理原材料
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
