<?php
/**
 * 配方控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Recipe.php';
require_once __DIR__ . '/../models/Product.php';
require_once __DIR__ . '/../models/Material.php';

class RecipeController extends Controller
{
    private $recipeModel;
    private $productModel;
    private $materialModel;

    public function __construct()
    {
        parent::__construct();
        $this->requireAuth();
        $this->recipeModel = new Recipe();
        $this->productModel = new Product();
        $this->materialModel = new Material();
    }

    /**
     * 配方列表页
     */
    public function index()
    {
        $search = $_GET['search'] ?? '';
        $productId = $_GET['product_id'] ?? '';
        
        if (!empty($search)) {
            $recipes = $this->recipeModel->search($search);
        } else {
            $recipes = $this->recipeModel->getAll();
        }
        
        // 如果指定了产品ID，筛选该产品的配方
        if (!empty($productId)) {
            $recipes = array_filter($recipes, function($recipe) use ($productId) {
                return $recipe['product_id'] == $productId;
            });
        }
        
        $stats = $this->recipeModel->getStats();
        
        $this->view('recipes/index', [
            'title' => '配方管理',
            'recipes' => $recipes,
            'stats' => $stats,
            'search' => $search,
            'productId' => $productId
        ]);
    }

    /**
     * 显示新增配方页面
     */
    public function create()
    {
        $productId = $_GET['product_id'] ?? '';
        
        // 获取所有成品
        $products = $this->productModel->getAll();
        
        // 获取所有原材料
        $materials = $this->materialModel->getAll();
        
        // 如果指定了产品ID，检查是否已有配方
        if (!empty($productId)) {
            $existingRecipe = $this->recipeModel->getByProductId($productId);
            if ($existingRecipe) {
                $this->setFlash('error', '该成品已有配方，请先删除现有配方');
                $this->redirect('index.php?controller=recipe&action=index');
            }
        }

        $this->view('recipes/create', [
            'title' => '创建配方',
            'products' => $products,
            'materials' => $materials,
            'selectedProductId' => $productId,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理新增配方请求
     */
    public function store()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=recipe&action=index');
        }

        $this->validateCsrfToken();

        $productId = $_POST['product_id'] ?? '';
        $recipeName = trim($_POST['recipe_name'] ?? '');
        $materials = $_POST['materials'] ?? [];

        // 验证输入
        if (empty($productId)) {
            $this->setFlash('error', '请选择成品');
            $this->redirect('index.php?controller=recipe&action=create');
        }

        if (empty($materials)) {
            $this->setFlash('error', '请至少添加一种原材料');
            $this->redirect('index.php?controller=recipe&action=create');
        }

        // 验证原材料数据
        $recipeItems = [];
        foreach ($materials as $material) {
            if (empty($material['material_id']) || empty($material['quantity']) || empty($material['unit'])) {
                $this->setFlash('error', '请完整填写所有原材料信息');
                $this->redirect('index.php?controller=recipe&action=create');
            }

            $recipeItems[] = [
                'material_id' => $material['material_id'],
                'quantity_needed' => floatval($material['quantity']),
                'unit_of_material' => $material['unit']
            ];
        }

        try {
            $recipeId = $this->recipeModel->create($productId, $recipeName, $recipeItems);
            
            $this->setFlash('success', '配方创建成功');
            $this->redirect('index.php?controller=recipe&action=view&id=' . $recipeId);
            
        } catch (Exception $e) {
            $this->setFlash('error', '创建失败：' . $e->getMessage());
            $this->redirect('index.php?controller=recipe&action=create');
        }
    }

    /**
     * 显示编辑配方页面
     */
    public function edit()
    {
        $id = $_GET['id'] ?? 0;
        $recipe = $this->recipeModel->getWithItems($id);
        
        if (!$recipe) {
            $this->setFlash('error', '配方不存在');
            $this->redirect('index.php?controller=recipe&action=index');
        }

        // 获取所有原材料
        $materials = $this->materialModel->getAll();

        $this->view('recipes/edit', [
            'title' => '编辑配方',
            'recipe' => $recipe,
            'materials' => $materials,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理更新配方请求
     */
    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=recipe&action=index');
        }

        $this->validateCsrfToken();

        $recipeId = $_POST['recipe_id'] ?? 0;
        $recipeName = trim($_POST['recipe_name'] ?? '');
        $materials = $_POST['materials'] ?? [];

        $recipe = $this->recipeModel->getById($recipeId);
        if (!$recipe) {
            $this->setFlash('error', '配方不存在');
            $this->redirect('index.php?controller=recipe&action=index');
        }

        // 验证原材料数据
        $recipeItems = [];
        if (!empty($materials)) {
            foreach ($materials as $material) {
                if (empty($material['material_id']) || empty($material['quantity']) || empty($material['unit'])) {
                    $this->setFlash('error', '请完整填写所有原材料信息');
                    $this->redirect('index.php?controller=recipe&action=edit&id=' . $recipeId);
                }

                $recipeItems[] = [
                    'material_id' => $material['material_id'],
                    'quantity_needed' => floatval($material['quantity']),
                    'unit_of_material' => $material['unit']
                ];
            }
        }

        try {
            $this->recipeModel->update($recipeId, $recipeName, $recipeItems);
            
            $this->setFlash('success', '配方更新成功');
            $this->redirect('index.php?controller=recipe&action=view&id=' . $recipeId);
            
        } catch (Exception $e) {
            $this->setFlash('error', '更新失败：' . $e->getMessage());
            $this->redirect('index.php?controller=recipe&action=edit&id=' . $recipeId);
        }
    }

    /**
     * 删除配方
     */
    public function delete()
    {
        $id = $_GET['id'] ?? 0;
        $recipe = $this->recipeModel->getById($id);
        
        if (!$recipe) {
            $this->setFlash('error', '配方不存在');
            $this->redirect('index.php?controller=recipe&action=index');
        }

        try {
            $this->recipeModel->delete($id);
            $this->setFlash('success', '配方删除成功');
        } catch (Exception $e) {
            $this->setFlash('error', '删除失败：' . $e->getMessage());
        }
        
        $this->redirect('index.php?controller=recipe&action=index');
    }

    /**
     * 查看配方详情
     */
    public function detail()
    {
        $id = $_GET['id'] ?? 0;
        $recipe = $this->recipeModel->getWithItems($id);

        if (!$recipe) {
            $this->setFlash('error', '配方不存在');
            $this->redirect('index.php?controller=recipe&action=index');
        }

        // 获取成品信息
        $product = $this->productModel->getById($recipe['product_id']);

        $this->view('recipes/view', [
            'title' => '配方详情',
            'recipe' => $recipe,
            'product' => $product
        ]);
    }
}
