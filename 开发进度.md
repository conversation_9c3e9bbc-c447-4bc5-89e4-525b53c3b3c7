# 麻糍工厂销售系统 - 开发进度计划

## 概述

本开发进度计划旨在为"麻糍工厂销售系统"项目提供一个清晰的任务分解和时间安排框架。项目将采用敏捷迭代的方式，分为若干关键阶段，每个阶段包含明确的开发任务和交付目标。以下计划基于选定的技术栈：PHP + MySQL + Bootstrap。

**总计预估时间：约 33 - 59 工作日** (这是一个相对粗略的估计，实际时间会因具体需求细节、开发效率和遇到的问题而有所调整。建议每周进行进度回顾和调整。)

---

## 阶段一：准备与设计

**预计时间：3 - 5 工作日**

**目标：** 完成项目启动前的所有准备工作，明确需求，完成核心设计，搭建好开发基础。

*   **任务 1.1：需求细化与最终确认**
    *   与您进行深入沟通，逐条确认 `test.md` 中的所有功能点和细节要求。
    *   明确数据项、业务规则、操作流程的边界条件。
    *   输出：更新后的需求确认清单或会议纪要。
*   **任务 1.2：数据库详细设计**
    *   根据确认的需求，完成所有数据表的详细字段设计（字段名、数据类型、长度、是否允许为空、默认值、索引、外键约束等）。
    *   绘制数据库ER图 (Entity Relationship Diagram)。
    *   输出：详细的数据库设计文档/ER图。
*   **任务 1.3：核心界面原型设计 (可选但强烈推荐)**
    *   使用简单的原型工具 (如Balsamiq, Figma的草图功能，甚至纸笔) 绘制系统主要操作界面的草图。
    *   包括：登录页、主操作界面框架、成品管理、原材料管理、销售开单、库存查看、客户账目等关键页面。
    *   明确主要的用户交互流程。
    *   输出：界面原型图/草稿。
*   **任务 1.4：开发环境搭建与配置**
    *   确保PHPStudy Pro环境正常运行，PHP版本、MySQL服务符合要求。
    *   在MySQL中创建 `test2` 数据库 (如果尚未完全配置好)。
    *   在本地工作区创建 `项目开发文档.md` 中规划的项目目录结构。
    *   下载并配置好Bootstrap及jQuery等前端依赖库到 `public/assets/` 目录。
*   **任务 1.5：版本控制系统初始化**
    *   在项目根目录初始化Git仓库。
    *   进行首次提交 (例如，包含初始的目录结构和文档)。

---

## 阶段二：基础框架搭建与用户认证模块

**预计时间：5 - 7 工作日**

**目标：** 搭建项目的基础代码框架，实现用户登录、登出及访问控制功能。

*   **任务 2.1：数据库连接模块实现**
    *   编写 `app/core/Database.php`，封装PDO或mysqli连接，提供统一的数据库连接获取和关闭方法。
    *   在 `app/config/database.php` 中配置数据库连接参数 (主机、用户名、密码、数据库名)。
*   **任务 2.2：基础布局与前端资源引入**
    *   创建基础的HTML布局文件 (如 `app/views/layouts/header.php`, `app/views/layouts/footer.php`, `app/views/layouts/navigation.php`)。
    *   在布局文件中引入Bootstrap CSS, 自定义CSS (`style.css`), jQuery, Bootstrap JS, 自定义JS (`app.js`)。
    *   设计一个简洁的全局导航栏和页脚。
*   **任务 2.3：用户数据表创建**
    *   根据数据库设计，在MySQL中创建 `users` 表。
*   **任务 2.4：用户模型 (User Model) 实现**
    *   创建 `app/models/User.php`。
    *   实现用户数据的基本操作方法，如根据用户名查找用户、验证密码等。
*   **任务 2.5：认证控制器 (AuthController) 实现**
    *   创建 `app/controllers/AuthController.php`。
    *   实现显示登录页面的方法。
    *   实现处理登录请求的方法：
        *   接收表单提交的用户名和密码。
        *   调用User模型进行用户查找和密码验证 (`password_verify()`)。
        *   登录成功后，设置PHP Session (`$_SESSION`) 存储用户信息，并跳转到主页。
        *   登录失败，显示错误提示。
    *   实现登出方法：销毁Session并跳转到登录页。
*   **任务 2.6：登录视图 (Login View) 创建**
    *   创建 `app/views/auth/login.php`。
    *   使用Bootstrap构建登录表单界面。
*   **任务 2.7：路由与请求分发 (基础版)**
    *   在 `public/index.php` 中实现简单的路由逻辑，根据URL参数调用相应的控制器和方法 (或者在 `app/routes.php` 中定义)。
    *   例如：`index.php?controller=auth&action=login`。
*   **任务 2.8：访问控制实现**
    *   在需要登录才能访问的页面或控制器方法执行前，检查Session中是否存在有效的用户信息。
    *   如果未登录，则重定向到登录页面。
    *   可以考虑在基础控制器 `app/core/Controller.php` (如果使用) 或每个控制器中实现此逻辑，或者通过一个统一的权限检查函数。

---

## 阶段三：核心模块 - 原材料、成品与配方管理

**预计时间：7 - 10 工作日**

**目标：** 实现对原材料、成品及它们之间配方关系的管理功能。

*   **任务 3.1：原材料管理**
    *   [ ] **数据表创建**: 在MySQL中创建 `materials` 表。
    *   [ ] **模型实现 (`Material.php`)**: 实现原材料的增、删、改、查 (列表及单个) 的数据操作方法。
    *   [ ] **控制器实现 (`MaterialController.php`)**: 实现处理原材料CRUD请求的方法，调用模型，加载相应视图。
    *   [ ] **视图实现 (`app/views/materials/`)**: 创建原材料列表页、新增/编辑表单页。使用Bootstrap美化界面。
*   **任务 3.2：成品管理**
    *   [ ] **数据表创建**: 在MySQL中创建 `products` 表。
    *   [ ] **模型实现 (`Product.php`)**: 实现成品的增、删、改、查的数据操作方法。
    *   [ ] **控制器实现 (`ProductController.php`)**: 实现处理成品CRUD请求的方法。
    *   [ ] **视图实现 (`app/views/products/`)**: 创建成品列表页、新增/编辑表单页。
*   **任务 3.3：配方管理**
    *   [ ] **数据表创建**: 在MySQL中创建 `recipes` 和 `recipe_items` 表，并设置好表间关联。
    *   [ ] **模型实现 (`Recipe.php`, 可能也包含 `RecipeItem.php` 或在Recipe模型中处理子项)**: 实现配方及其明细的创建、读取、更新、删除逻辑。一个配方关联一个成品和多个原材料及其所需数量。
    *   [ ] **控制器实现 (可能合并到 `ProductController.php` 或新建 `RecipeController.php`)**: 实现配方管理的相关操作请求处理。
    *   [ ] **视图实现 (可能在成品管理视图中集成或新建 `app/views/recipes/`)**: 实现创建/编辑配方的界面，允许选择成品，并动态添加/编辑/删除配方中的原材料及其数量。

---

## 阶段四：核心业务流程 - 销售、库存与客户账目

**预计时间：10 - 15 工作日**

**目标：** 实现系统的核心业务流程，包括销售开单、库存自动扣减、库存手动调整以及客户账目管理。

*   **任务 4.1：客户管理基础**
    *   [ ] **数据表创建**: 在MySQL中创建 `customers` 表。
    *   [ ] **模型实现 (`Customer.php`)**: 实现客户信息的增、删、改、查。
    *   [ ] **控制器实现 (`CustomerController.php`)**: 处理客户信息管理的请求。
    *   [ ] **视图实现 (`app/views/customers/`)**: 创建客户列表、新增/编辑客户界面。
*   **任务 4.2：销售订单管理**
    *   [ ] **数据表创建**: 在MySQL中创建 `sales_orders`, `sales_order_items` 表。
    *   [ ] **模型实现 (`Sale.php` - 包含订单和订单明细逻辑)**: 实现创建销售订单、添加订单明细、查询订单等数据操作。
    *   [ ] **控制器实现 (`SaleController.php`)**: 
        *   处理显示销售开单页面的请求。
        *   处理保存销售订单的请求：
            *   数据验证。
            *   保存订单头信息到 `sales_orders`。
            *   保存订单明细到 `sales_order_items`。
            *   **调用库存扣减逻辑 (见任务4.3)**。
            *   **更新客户欠款 (见任务4.5)**。
        *   处理查询销售订单列表及详情的请求。
    *   [ ] **视图实现 (`app/views/sales/`)**: 创建销售开单页面 (支持选择客户、动态添加成品及数量、显示总金额等)、销售订单列表页、订单详情页。
*   **任务 4.3：库存管理与自动扣减**
    *   [ ] **数据表创建**: 在MySQL中创建 `inventory_transactions` 表 (用于记录所有库存变动流水)。
    *   [ ] **库存扣减逻辑实现 (可在 `SaleController.php` 中调用，或封装到独立的库存服务类/模型方法中)**:
        *   当销售订单保存成功后，遍历订单中的每个成品。
        *   根据该成品的配方 (查询 `recipes` 和 `recipe_items` 表)，计算需要消耗的各种原材料数量。
        *   更新 `materials` 表中对应原材料的 `stock_quantity` (允许负库存)。
        *   在 `inventory_transactions` 表中为每种消耗的原材料记录一条类型为 'production_consumption' (生产消耗) 的流水。
        *   更新 `products` 表中对应成品的 `stock_quantity` (允许负库存)。
        *   在 `inventory_transactions` 表中为售出的成品记录一条类型为 'sale' (销售出库) 的流水。
    *   [ ] **库存调整功能**:
        *   **控制器实现 (新建 `InventoryController.php` 或在 `MaterialController.php` / `ProductController.php` 中扩展)**: 实现手动调整原材料和成品库存的功能。
        *   **视图实现**: 提供简单的表单用于选择物料/成品，输入调整数量（增加/减少）和备注。
        *   **后端逻辑**: 更新对应物料/成品的 `stock_quantity`，并在 `inventory_transactions` 表中记录 'adjustment_in' (手动入库) 或 'adjustment_out' (手动出库) 的流水。
    *   [ ] **库存查询界面**: 在 `app/views/inventory/` 中创建页面显示当前原材料和成品的库存列表 (从 `materials` 和 `products` 表读取)。
*   **任务 4.4：客户账目管理**
    *   [ ] **数据表创建**: 在MySQL中创建 `customer_ledger_entries` 表 (客户往来账目流水)。
    *   [ ] **账目更新逻辑 (在 `SaleController.php` 保存订单时，或客户付款时调用)**:
        *   销售开单时：增加客户欠款，即在 `customers` 表更新 `balance` 字段 (应收增加)，并在 `customer_ledger_entries` 表记录一笔借方流水。
        *   (未来扩展) 客户付款时：减少客户欠款，更新 `balance`，记录一笔贷方流水。
    *   [ ] **客户账目查看**: 
        *   在客户列表页显示每个客户的当前欠款 (`customers.balance`)。
        *   实现查看指定客户的往来账目流水详情页 (从 `customer_ledger_entries` 读取)。
*   **任务 4.5：(简化) 原材料采购现结处理**
    *   由于是现结，可以简化为直接通过"库存调整"功能增加原材料库存。
    *   在进行原材料的"手动入库"调整时，可以考虑增加一个"采购单价"的输入项，用于后续成本核算（如果需要）。对应的 `inventory_transactions` 流水可以增加一个 `unit_price` 字段。

---

## 阶段五：系统完善、测试与优化

**预计时间：7 - 10 工作日**

**目标：** 全面测试系统功能，优化用户体验和性能，修复Bug，准备上线。

*   **任务 5.1：界面美化与移动端适配**
    *   使用Bootstrap的响应式类库和组件，仔细调整所有页面的布局和元素，确保在不同屏幕尺寸（尤其是手机端）下显示良好，操作便捷。
    *   统一应用的视觉风格，确保界面扁平化、卡片式设计的美观性。
*   **任务 5.2：用户体验优化**
    *   检查所有操作流程是否顺畅，提示信息是否清晰友好。
    *   对于耗时操作，考虑添加加载提示。
    *   优化表单的易用性，如使用合适的输入类型、添加必要的帮助信息等。
*   **任务 5.3：输入验证强化 (前后端)**
    *   前端：使用HTML5表单验证属性和/或JavaScript进行基本的输入格式、必填项校验。
    *   后端：在PHP控制器中对所有接收到的用户输入进行严格的服务端验证 (类型、范围、格式、存在性等)。这是安全的关键。
*   **任务 5.4：错误处理与异常捕获**
    *   在PHP中设置合理的错误报告级别 (开发时开启所有错误，生产环境记录到日志)。
    *   对数据库操作等可能抛出异常的代码块使用 `try-catch` 进行捕获和友好处理。
    *   向用户显示易懂的错误提示信息，而不是直接暴露PHP错误详情。
*   **任务 5.5：安全性检查与加固**
    *   **SQL注入**: 再次确认所有数据库查询都使用了预处理语句 (PDO prepared statements 或 mysqli_prepare)。
    *   **XSS (跨站脚本)**: 确认所有输出到HTML的用户提交内容都经过了 `htmlspecialchars()` 或类似函数转义。
    *   **CSRF (跨站请求伪造)**: 对于所有执行状态更改的操作（如删除、修改），考虑添加CSRF Token保护机制 (对于简单内部系统，可酌情简化)。
    *   **文件上传安全 (如果未来有)**: 严格限制上传文件类型和大小，对文件名进行处理。
    *   **目录权限**: 确保Web服务器对项目文件有正确的读写权限，敏感配置文件不应放在Web可直接访问目录下或进行保护。
*   **任务 5.6：功能集成测试**
    *   设计测试用例，覆盖所有核心功能模块和业务流程。
    *   模拟用户实际操作场景进行端到端测试。
    *   记录发现的Bug，并及时修复。
*   **任务 5.7：性能初步评估与优化**
    *   对于数据量可能较大的查询（如库存列表、订单列表、客户账目流水），检查对应的MySQL表是否已经建立了合适的索引。
    *   使用浏览器开发者工具分析页面加载速度，优化图片大小、合并CSS/JS文件等 (对于小系统，此项可简化)。
*   **任务 5.8：编写用户操作手册**
    *   为最终用户编写一份简单明了的系统操作指南，说明各项功能如何使用。
    *   可以包含截图和步骤说明。

---

## 阶段六：部署与交付 (预计 1-2 天)**
*   **任务 6.1：生产环境配置确认**
    *   确认宝塔面板环境配置：
        *   网站根目录：`test-cr`
        *   网站运行目录：`public`
        *   确保PHP版本、MySQL版本、所需扩展等符合要求
    *   确保所有需要通过浏览器访问的文件都放置在public目录下
*   **任务 6.2：代码部署**
    *   将项目代码完整部署到用户服务器的指定Web目录。
    *   配置好数据库连接文件 (`app/config/database.php`)。
    *   确保URL路径和文件引用路径正确，考虑到运行目录为public的特殊性
*   **任务 6.3：数据初始化 (可选)**
    *   如果需要，导入初始的客户数据、原材料数据、成品数据等。
    *   执行数据库表结构创建脚本 (如果之前未在生产库创建)。
    *   将初始化脚本放在public目录下以便通过浏览器访问
*   **任务 6.4：最终验收测试**
    *   与用户一起在实际环境中对系统主要功能进行最后一遍测试确认。
*   **任务 6.5：用户培训**
    *   根据编写的操作手册，对不超过5名的用户进行系统操作培训。
    *   解答用户提出的疑问。
*   **任务 6.6：收集初期反馈与微调**
    *   系统上线初期，密切关注用户使用情况，收集反馈意见。
    *   对于发现的小问题或易用性改进点，进行快速修复和调整。

---

本文档将作为项目开发的主要进度参考，并会根据实际情况在项目执行过程中进行动态调整。 