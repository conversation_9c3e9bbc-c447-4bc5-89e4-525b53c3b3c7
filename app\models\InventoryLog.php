<?php
/**
 * 库存变动记录模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class InventoryLog
{
    private $db;
    private $table = 'inventory_logs';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 记录库存变动
     */
    public function log($itemType, $itemId, $changeType, $quantityBefore, $quantityChange, $quantityAfter, $options = [])
    {
        $unitPrice = $options['unit_price'] ?? null;
        $totalValue = $options['total_value'] ?? null;
        $reason = $options['reason'] ?? null;
        $referenceType = $options['reference_type'] ?? null;
        $referenceId = $options['reference_id'] ?? null;
        $operatorId = $options['operator_id'] ?? $_SESSION['user_id'] ?? 1;

        $sql = "INSERT INTO {$this->table} 
                (item_type, item_id, change_type, quantity_before, quantity_change, quantity_after, 
                 unit_price, total_value, reason, reference_type, reference_id, operator_id, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $itemType,
            $itemId,
            $changeType,
            $quantityBefore,
            $quantityChange,
            $quantityAfter,
            $unitPrice,
            $totalValue,
            $reason,
            $referenceType,
            $referenceId,
            $operatorId
        ];

        $this->db->execute($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * 获取指定物品的库存变动记录
     */
    public function getByItem($itemType, $itemId, $limit = 50)
    {
        $sql = "
            SELECT il.*, u.name as operator_name,
                   CASE 
                       WHEN il.item_type = 'material' THEN m.name
                       WHEN il.item_type = 'product' THEN p.name
                   END as item_name
            FROM {$this->table} il
            LEFT JOIN users u ON il.operator_id = u.id
            LEFT JOIN materials m ON il.item_type = 'material' AND il.item_id = m.id
            LEFT JOIN products p ON il.item_type = 'product' AND il.item_id = p.id
            WHERE il.item_type = ? AND il.item_id = ?
            ORDER BY il.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$itemType, $itemId, $limit]);
    }

    /**
     * 获取所有库存变动记录
     */
    public function getAll($filters = [], $limit = 100, $offset = 0)
    {
        $where = [];
        $params = [];

        if (!empty($filters['item_type'])) {
            $where[] = "il.item_type = ?";
            $params[] = $filters['item_type'];
        }

        if (!empty($filters['change_type'])) {
            $where[] = "il.change_type = ?";
            $params[] = $filters['change_type'];
        }

        if (!empty($filters['start_date'])) {
            $where[] = "DATE(il.created_at) >= ?";
            $params[] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $where[] = "DATE(il.created_at) <= ?";
            $params[] = $filters['end_date'];
        }

        $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);

        $sql = "
            SELECT il.*, u.name as operator_name,
                   CASE 
                       WHEN il.item_type = 'material' THEN m.name
                       WHEN il.item_type = 'product' THEN p.name
                   END as item_name,
                   CASE 
                       WHEN il.item_type = 'material' THEN m.unit
                       WHEN il.item_type = 'product' THEN p.unit
                   END as item_unit
            FROM {$this->table} il
            LEFT JOIN users u ON il.operator_id = u.id
            LEFT JOIN materials m ON il.item_type = 'material' AND il.item_id = m.id
            LEFT JOIN products p ON il.item_type = 'product' AND il.item_id = p.id
            {$whereClause}
            ORDER BY il.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 获取库存变动统计
     */
    public function getStats($startDate = null, $endDate = null)
    {
        $where = [];
        $params = [];

        if ($startDate) {
            $where[] = "DATE(created_at) >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $where[] = "DATE(created_at) <= ?";
            $params[] = $endDate;
        }

        $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);

        $stats = [];

        // 按变动类型统计
        $sql = "
            SELECT change_type, COUNT(*) as count, 
                   SUM(ABS(quantity_change)) as total_quantity,
                   SUM(ABS(total_value)) as total_value
            FROM {$this->table} 
            {$whereClause}
            GROUP BY change_type
        ";
        $stats['by_type'] = $this->db->fetchAll($sql, $params);

        // 按物品类型统计
        $sql = "
            SELECT item_type, COUNT(*) as count,
                   SUM(ABS(quantity_change)) as total_quantity,
                   SUM(ABS(total_value)) as total_value
            FROM {$this->table} 
            {$whereClause}
            GROUP BY item_type
        ";
        $stats['by_item_type'] = $this->db->fetchAll($sql, $params);

        // 总体统计
        $sql = "
            SELECT COUNT(*) as total_records,
                   SUM(ABS(quantity_change)) as total_quantity_change,
                   SUM(ABS(total_value)) as total_value_change
            FROM {$this->table} 
            {$whereClause}
        ";
        $stats['summary'] = $this->db->fetch($sql, $params);

        return $stats;
    }

    /**
     * 获取变动类型的中文名称
     */
    public static function getChangeTypeText($changeType)
    {
        $types = [
            'in' => '入库',
            'out' => '出库',
            'adjust' => '调整',
            'produce' => '生产',
            'sale' => '销售',
            'return' => '退货'
        ];

        return $types[$changeType] ?? $changeType;
    }

    /**
     * 删除指定日期之前的记录（数据清理）
     */
    public function cleanup($beforeDate, $keepDays = 365)
    {
        $sql = "DELETE FROM {$this->table} WHERE created_at < DATE_SUB(?, INTERVAL ? DAY)";
        return $this->db->execute($sql, [$beforeDate, $keepDays]);
    }
}
