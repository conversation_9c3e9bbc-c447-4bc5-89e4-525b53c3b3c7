<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-plus-circle text-primary"></i> 快速开单</h2>
        <p class="text-muted mb-0">创建新的销售单</p>
    </div>
    <div>
        <a href="index.php?controller=sale&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 开单表单 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart"></i> 销售信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=sale&action=store" id="saleForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <!-- 客户选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">
                                    <i class="fas fa-user"></i> 选择客户 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="customer_id" name="customer_id" required>
                                    <option value="">请选择客户</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>" 
                                                data-balance="<?php echo $customer['balance']; ?>"
                                                <?php echo $selectedCustomerId == $customer['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if ($customer['balance'] > 0): ?>
                                                (欠款: ¥<?php echo number_format($customer['balance'], 2); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">选择购买客户</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-info-circle"></i> 客户信息
                                </label>
                                <div id="customer-info" class="form-control-plaintext">
                                    <small class="text-muted">请先选择客户</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品列表 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-box"></i> 销售商品</h6>
                            <button type="button" class="btn btn-sm btn-success" id="add-product">
                                <i class="fas fa-plus"></i> 添加商品
                            </button>
                        </div>
                        
                        <div id="products-container">
                            <!-- 商品行将通过JavaScript动态添加 -->
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle"></i> 开单说明：</h6>
                            <ul class="mb-0">
                                <li>系统会自动扣减商品库存</li>
                                <li>未收款部分会自动记入客户欠款</li>
                                <li>可以修改商品单价（默认为系统售价）</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 金额汇总 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">金额汇总</h6>
                                    <table class="table table-sm table-borderless mb-0">
                                        <tr>
                                            <td>商品总额:</td>
                                            <td class="text-end"><strong id="total-amount">¥0.00</strong></td>
                                        </tr>
                                        <tr>
                                            <td>本次收款:</td>
                                            <td class="text-end">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">¥</span>
                                                    <input type="number" class="form-control" id="payment_amount" 
                                                           name="payment_amount" step="0.01" value="0.00">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="border-top">
                                            <td><strong>欠款金额:</strong></td>
                                            <td class="text-end"><strong id="debt-amount" class="text-danger">¥0.00</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">
                                    <i class="fas fa-sticky-note"></i> 备注
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="4" 
                                          placeholder="销售备注信息..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=sale&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 创建销售单
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 商品行模板 -->
<template id="product-row-template">
    <div class="product-row border rounded p-3 mb-3">
        <div class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">商品</label>
                <select class="form-control product-select" name="items[][product_id]" required>
                    <option value="">请选择商品</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['id']; ?>" 
                                data-unit="<?php echo htmlspecialchars($product['unit']); ?>"
                                data-stock="<?php echo $product['stock_quantity']; ?>"
                                data-price="<?php echo $product['selling_price']; ?>">
                            <?php echo htmlspecialchars($product['name']); ?>
                            (库存: <?php echo $product['stock_quantity']; ?><?php echo htmlspecialchars($product['unit']); ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">数量</label>
                <input type="number" class="form-control quantity-input" name="items[][quantity]" 
                       min="0" step="0.001" placeholder="0.000" required>
            </div>
            <div class="col-md-2">
                <label class="form-label">单价</label>
                <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input type="number" class="form-control price-input" name="items[][unit_price]" 
                           min="0" step="0.01" placeholder="0.00" required>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">小计</label>
                <div class="form-control-plaintext subtotal">¥0.00</div>
            </div>
            <div class="col-md-2">
                <label class="form-label">库存</label>
                <div class="form-control-plaintext stock-info text-muted">-</div>
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-outline-danger btn-sm remove-product w-100">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
</template>

<script>
// 确保DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing sale create page...');

    // 检查jQuery是否加载
    if (typeof $ === 'undefined') {
        console.error('jQuery not loaded!');
        return;
    }

    console.log('jQuery loaded, setting up event handlers...');

    // 添加商品行
    $('#add-product').on('click', function() {
        console.log('Add product button clicked');
        var template = $('#product-row-template').html();
        if (!template) {
            console.error('Product template not found!');
            return;
        }
        $('#products-container').append(template);
        console.log('Product row added');
    });

    // 删除商品行
    $(document).on('click', '.remove-product', function() {
        console.log('Remove product button clicked');
        $(this).closest('.product-row').remove();
        calculateTotal();
    });

    // 商品选择变化
    $(document).on('change', '.product-select', function() {
        var $row = $(this).closest('.product-row');
        var $option = $(this).find('option:selected');

        var unit = $option.data('unit') || '';
        var stock = $option.data('stock') || 0;
        var price = $option.data('price') || 0;

        $row.find('.price-input').val(price.toFixed(2));
        $row.find('.stock-info').text(stock + unit);

        calculateTotal();
    });

    // 数量或单价变化
    $(document).on('input', '.quantity-input, .price-input', function() {
        var $row = $(this).closest('.product-row');
        var quantity = parseFloat($row.find('.quantity-input').val()) || 0;
        var price = parseFloat($row.find('.price-input').val()) || 0;
        var subtotal = quantity * price;

        $row.find('.subtotal').text('¥' + subtotal.toFixed(2));
        calculateTotal();
    });

    // 收款金额变化
    $('#payment_amount').on('input', function() {
        calculateTotal();
    });

    // 客户选择变化
    $('#customer_id').on('change', function() {
        var $option = $(this).find('option:selected');
        var balance = parseFloat($option.data('balance')) || 0;

        if ($(this).val()) {
            var info = $option.text();
            if (balance > 0) {
                info += '<br><small class="text-danger">当前欠款: ¥' + balance.toFixed(2) + '</small>';
            } else if (balance < 0) {
                info += '<br><small class="text-success">预付余额: ¥' + Math.abs(balance).toFixed(2) + '</small>';
            } else {
                info += '<br><small class="text-success">账目结清</small>';
            }
            $('#customer-info').html(info);
        } else {
            $('#customer-info').html('<small class="text-muted">请先选择客户</small>');
        }
    });

    // 计算总金额
    function calculateTotal() {
        var total = 0;
        $('.product-row').each(function() {
            var quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            var price = parseFloat($(this).find('.price-input').val()) || 0;
            total += quantity * price;
        });

        var payment = parseFloat($('#payment_amount').val()) || 0;
        var debt = total - payment;

        $('#total-amount').text('¥' + total.toFixed(2));
        $('#debt-amount').text('¥' + debt.toFixed(2));

        // 自动设置收款金额为总金额
        if ($('#payment_amount').val() === '' || $('#payment_amount').val() === '0.00') {
            $('#payment_amount').val(total.toFixed(2));
            $('#debt-amount').text('¥0.00');
        }
    }

    // 表单验证
    $('#saleForm').on('submit', function(e) {
        var productRows = $('.product-row').length;
        if (productRows === 0) {
            e.preventDefault();
            alert('请至少添加一个商品');
            return false;
        }

        var hasEmptyProduct = false;
        $('.product-row').each(function() {
            var productId = $(this).find('.product-select').val();
            var quantity = $(this).find('.quantity-input').val();
            var price = $(this).find('.price-input').val();

            if (!productId || !quantity || !price || parseFloat(quantity) <= 0 || parseFloat(price) <= 0) {
                hasEmptyProduct = true;
                return false;
            }
        });

        if (hasEmptyProduct) {
            e.preventDefault();
            alert('请完整填写所有商品信息，且数量和单价必须大于0');
            return false;
        }
    });

    // 初始添加一行
    setTimeout(function() {
        $('#add-product').trigger('click');
        console.log('Initial product row added');
    }, 100);

    // 将calculateTotal函数暴露到全局作用域
    window.calculateTotal = calculateTotal;
});

// 备用的jQuery ready方法
$(document).ready(function() {
    console.log('jQuery ready fired for sale create page');

    // 如果按钮还没有绑定事件，重新绑定
    if (!$('#add-product').data('events')) {
        console.log('Re-binding add product button');
        $('#add-product').off('click').on('click', function() {
            console.log('Add product button clicked (backup handler)');
            var template = $('#product-row-template').html();
            if (template) {
                $('#products-container').append(template);
            } else {
                console.error('Product template not found in backup handler!');
            }
        });
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
