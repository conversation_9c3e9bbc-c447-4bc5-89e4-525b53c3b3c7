<?php
/**
 * 配方模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class Recipe
{
    private $db;
    private $table = 'recipes';
    private $itemsTable = 'recipe_items';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 根据成品ID获取配方
     */
    public function getByProductId($productId)
    {
        $sql = "SELECT * FROM {$this->table} WHERE product_id = ? LIMIT 1";
        return $this->db->fetch($sql, [$productId]);
    }

    /**
     * 根据ID获取配方
     */
    public function getById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? LIMIT 1";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 获取配方详情（包含配方项目）
     */
    public function getWithItems($recipeId)
    {
        $recipe = $this->getById($recipeId);
        if (!$recipe) {
            return null;
        }

        $items = $this->getRecipeItems($recipeId);
        $recipe['items'] = $items;

        return $recipe;
    }

    /**
     * 获取配方项目
     */
    public function getRecipeItems($recipeId)
    {
        $sql = "
            SELECT ri.*, m.name as material_name, m.unit as material_unit, m.stock_quantity as material_stock
            FROM {$this->itemsTable} ri 
            JOIN materials m ON ri.material_id = m.id 
            WHERE ri.recipe_id = ?
            ORDER BY m.name
        ";
        return $this->db->fetchAll($sql, [$recipeId]);
    }

    /**
     * 创建配方
     */
    public function create($productId, $name = null, $items = [])
    {
        try {
            $this->db->beginTransaction();

            // 检查是否已存在配方
            $existing = $this->getByProductId($productId);
            if ($existing) {
                throw new Exception('该成品已有配方，请先删除现有配方');
            }

            // 创建配方主记录
            $sql = "INSERT INTO {$this->table} (product_id, name, created_at, updated_at) VALUES (?, ?, NOW(), NOW())";
            $this->db->execute($sql, [$productId, $name]);
            $recipeId = $this->db->lastInsertId();

            // 添加配方项目
            if (!empty($items)) {
                $this->addRecipeItems($recipeId, $items);
            }

            $this->db->commit();
            return $recipeId;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 更新配方
     */
    public function update($recipeId, $name = null, $items = [])
    {
        try {
            $this->db->beginTransaction();

            // 更新配方主记录
            if ($name !== null) {
                $sql = "UPDATE {$this->table} SET name = ?, updated_at = NOW() WHERE id = ?";
                $this->db->execute($sql, [$name, $recipeId]);
            } else {
                // 即使名称没有变化，也更新时间戳
                $sql = "UPDATE {$this->table} SET updated_at = NOW() WHERE id = ?";
                $this->db->execute($sql, [$recipeId]);
            }

            // 删除现有配方项目
            $this->deleteRecipeItems($recipeId);

            // 添加新的配方项目
            if (!empty($items)) {
                $this->addRecipeItems($recipeId, $items);
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 删除配方
     */
    public function delete($recipeId)
    {
        try {
            $this->db->beginTransaction();

            // 删除配方项目
            $this->deleteRecipeItems($recipeId);

            // 删除配方主记录
            $sql = "DELETE FROM {$this->table} WHERE id = ?";
            $this->db->execute($sql, [$recipeId]);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 添加配方项目
     */
    private function addRecipeItems($recipeId, $items)
    {
        $sql = "INSERT INTO {$this->itemsTable} (recipe_id, material_id, quantity_needed, unit_of_material) VALUES (?, ?, ?, ?)";
        
        foreach ($items as $item) {
            $this->db->execute($sql, [
                $recipeId,
                $item['material_id'],
                $item['quantity_needed'],
                $item['unit_of_material']
            ]);
        }
    }

    /**
     * 删除配方项目
     */
    private function deleteRecipeItems($recipeId)
    {
        $sql = "DELETE FROM {$this->itemsTable} WHERE recipe_id = ?";
        $this->db->execute($sql, [$recipeId]);
    }

    /**
     * 获取所有配方列表
     */
    public function getAll()
    {
        $sql = "
            SELECT r.*, p.name as product_name, p.unit as product_unit,
                   (SELECT COUNT(*) FROM recipe_items ri WHERE ri.recipe_id = r.id) as material_count
            FROM {$this->table} r
            JOIN products p ON r.product_id = p.id
            ORDER BY p.name
        ";
        return $this->db->fetchAll($sql);
    }

    /**
     * 根据配方计算原材料消耗
     */
    public function calculateMaterialConsumption($recipeId, $productQuantity)
    {
        $items = $this->getRecipeItems($recipeId);
        $consumption = [];

        foreach ($items as $item) {
            $consumption[] = [
                'material_id' => $item['material_id'],
                'material_name' => $item['material_name'],
                'quantity_needed' => $item['quantity_needed'] * $productQuantity,
                'unit' => $item['unit_of_material'],
                'current_stock' => $item['material_stock']
            ];
        }

        return $consumption;
    }

    /**
     * 检查是否可以生产指定数量的成品
     */
    public function canProduce($recipeId, $productQuantity)
    {
        $consumption = $this->calculateMaterialConsumption($recipeId, $productQuantity);
        
        foreach ($consumption as $item) {
            if ($item['current_stock'] < $item['quantity_needed']) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取配方统计信息
     */
    public function getStats()
    {
        $stats = [];

        // 总配方数
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total_recipes'] = $result['count'];

        // 有配方的成品数
        $result = $this->db->fetch("SELECT COUNT(DISTINCT product_id) as count FROM {$this->table}");
        $stats['products_with_recipe'] = $result['count'];

        // 无配方的成品数
        $result = $this->db->fetch("
            SELECT COUNT(*) as count FROM products p 
            WHERE NOT EXISTS (SELECT 1 FROM {$this->table} r WHERE r.product_id = p.id)
        ");
        $stats['products_without_recipe'] = $result['count'];

        return $stats;
    }

    /**
     * 搜索配方
     */
    public function search($keyword)
    {
        $sql = "
            SELECT r.*, p.name as product_name, p.unit as product_unit,
                   (SELECT COUNT(*) FROM recipe_items ri WHERE ri.recipe_id = r.id) as material_count
            FROM {$this->table} r
            JOIN products p ON r.product_id = p.id
            WHERE p.name LIKE ? OR r.name LIKE ?
            ORDER BY p.name
        ";
        $searchTerm = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }
}
