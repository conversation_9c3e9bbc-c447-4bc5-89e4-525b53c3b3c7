<?php
/**
 * 直接测试MaterialController
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?controller=auth&action=login');
    exit;
}

echo "<h2>直接测试MaterialController</h2>";

try {
    // 加载必要文件
    require_once '../app/core/Controller.php';
    require_once '../app/controllers/MaterialController.php';
    
    echo "✅ 控制器文件加载成功<br>";
    
    // 创建控制器实例
    $controller = new MaterialController();
    echo "✅ MaterialController实例化成功<br>";
    
    echo "<h3>开始调用index方法</h3>";
    echo "如果页面在这里停止或重定向，说明问题在index方法中<br>";
    
    // 调用index方法
    $controller->index();
    
    echo "✅ index方法执行完成<br>";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
