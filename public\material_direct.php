<?php
/**
 * 直接测试MaterialController
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['user_id'])) {
    echo "❌ 用户未登录<br>";
    exit;
}

echo "<h2>直接测试MaterialController</h2>";
echo "用户ID: " . $_SESSION['user_id'] . "<br><br>";

try {
    echo "<h3>步骤1: 加载文件</h3>";
    require_once '../app/core/Controller.php';
    echo "✅ Controller.php 加载成功<br>";

    require_once '../app/controllers/MaterialController.php';
    echo "✅ MaterialController.php 加载成功<br>";

    echo "<h3>步骤2: 实例化控制器</h3>";
    echo "开始实例化MaterialController...<br>";

    $controller = new MaterialController();
    echo "✅ MaterialController实例化成功<br>";

    echo "<h3>步骤3: 测试认证方法</h3>";
    // 使用反射来测试私有方法
    $reflection = new ReflectionClass($controller);
    $isLoggedInMethod = $reflection->getMethod('isLoggedIn');
    $isLoggedInMethod->setAccessible(true);

    $isLoggedIn = $isLoggedInMethod->invoke($controller);
    echo "isLoggedIn() 返回: " . ($isLoggedIn ? 'true' : 'false') . "<br>";

    if (!$isLoggedIn) {
        echo "❌ 认证检查失败，这就是重定向循环的原因！<br>";
        echo "会话数据：<br>";
        echo "<pre>";
        print_r($_SESSION);
        echo "</pre>";
        exit;
    }

    echo "<h3>步骤4: 手动调用index方法</h3>";
    echo "开始调用index方法...<br>";
    echo "如果页面在这里停止，说明问题在index方法的执行过程中<br>";

    // 使用输出缓冲来捕获输出
    ob_start();
    $controller->index();
    $output = ob_get_contents();
    ob_end_clean();

    echo "✅ index方法执行完成<br>";
    echo "输出长度: " . strlen($output) . " 字符<br>";

    // 检查是否有重定向头
    $headers = headers_list();
    if (!empty($headers)) {
        echo "发送的HTTP头：<br>";
        foreach ($headers as $header) {
            echo "- " . $header . "<br>";
            if (strpos($header, 'Location:') === 0) {
                echo "❌ 发现重定向头！<br>";
            }
        }
    }

    echo "<h3>步骤5: 显示输出</h3>";
    echo "以下是index方法的输出：<br>";
    echo "<hr>";
    echo $output;

} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    echo "<h4>错误堆栈：</h4>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
