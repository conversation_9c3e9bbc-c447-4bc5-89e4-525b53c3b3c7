-- 创建库存管理相关表
-- 麻糍工厂销售系统

-- 库存变动记录表
CREATE TABLE IF NOT EXISTS `inventory_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_type` enum('material','product') NOT NULL COMMENT '物品类型：原材料或成品',
  `item_id` int(11) NOT NULL COMMENT '物品ID',
  `change_type` enum('in','out','adjust','produce','sale','return') NOT NULL COMMENT '变动类型',
  `quantity_before` decimal(10,3) NOT NULL COMMENT '变动前数量',
  `quantity_change` decimal(10,3) NOT NULL COMMENT '变动数量（正数为增加，负数为减少）',
  `quantity_after` decimal(10,3) NOT NULL COMMENT '变动后数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT NULL COMMENT '总价值',
  `reason` varchar(200) DEFAULT NULL COMMENT '变动原因',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型（如sale、recipe等）',
  `reference_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_item_type_id` (`item_type`, `item_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_operator` (`operator_id`),
  FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存变动记录表';

-- 库存预警设置表
CREATE TABLE IF NOT EXISTS `inventory_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_type` enum('material','product') NOT NULL,
  `item_id` int(11) NOT NULL,
  `min_quantity` decimal(10,3) NOT NULL COMMENT '最低库存量',
  `max_quantity` decimal(10,3) DEFAULT NULL COMMENT '最高库存量',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_item` (`item_type`, `item_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存预警设置表';

-- 库存盘点记录表
CREATE TABLE IF NOT EXISTS `inventory_counts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `count_date` date NOT NULL COMMENT '盘点日期',
  `status` enum('draft','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '盘点状态',
  `notes` text COMMENT '盘点备注',
  `operator_id` int(11) NOT NULL COMMENT '盘点员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_count_date` (`count_date`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点记录表';

-- 库存盘点明细表
CREATE TABLE IF NOT EXISTS `inventory_count_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `count_id` int(11) NOT NULL,
  `item_type` enum('material','product') NOT NULL,
  `item_id` int(11) NOT NULL,
  `system_quantity` decimal(10,3) NOT NULL COMMENT '系统库存数量',
  `actual_quantity` decimal(10,3) NOT NULL COMMENT '实际盘点数量',
  `difference` decimal(10,3) NOT NULL COMMENT '差异数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `difference_value` decimal(10,2) DEFAULT NULL COMMENT '差异金额',
  `notes` varchar(200) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_count_id` (`count_id`),
  KEY `idx_item` (`item_type`, `item_id`),
  FOREIGN KEY (`count_id`) REFERENCES `inventory_counts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点明细表';
