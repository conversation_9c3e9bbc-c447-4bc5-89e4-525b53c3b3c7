<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-heartbeat"></i> <?php echo $title; ?></h2>
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="runFullCheck()">
            <i class="fas fa-search"></i> 完整检查
        </button>
        <button type="button" class="btn btn-info" onclick="runQuickCheck()">
            <i class="fas fa-bolt"></i> 快速检查
        </button>
        <button type="button" class="btn btn-success" onclick="startAutoMonitor()">
            <i class="fas fa-play"></i> 自动监控
        </button>
    </div>
</div>

<!-- 快速状态概览 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> 系统状态概览</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="status-indicator <?php echo $quickCheck['database'] === 'pass' ? 'status-pass' : 'status-fail'; ?>">
                                <i class="fas fa-database fa-2x"></i>
                            </div>
                            <h6 class="mt-2">数据库</h6>
                            <small class="text-muted">
                                <?php echo $quickCheck['database'] === 'pass' ? '正常' : '异常'; ?>
                            </small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="status-indicator <?php echo $quickCheck['tables'] === 'pass' ? 'status-pass' : 'status-fail'; ?>">
                                <i class="fas fa-table fa-2x"></i>
                            </div>
                            <h6 class="mt-2">数据表</h6>
                            <small class="text-muted">
                                <?php echo $quickCheck['tables'] === 'pass' ? '正常' : '异常'; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-clock"></i> 实时监控</h6>
            </div>
            <div class="card-body">
                <div id="realtime-status">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">检查中...</span>
                        </div>
                        <p class="mt-2">正在获取实时状态...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 检查结果显示区域 -->
<div id="check-results" style="display: none;">
    <!-- 动态加载检查结果 -->
</div>

<!-- 自动监控控制面板 -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-cogs"></i> 监控设置</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="monitor-interval" class="form-label">监控间隔</label>
                    <select class="form-control" id="monitor-interval">
                        <option value="30">30秒</option>
                        <option value="60" selected>1分钟</option>
                        <option value="300">5分钟</option>
                        <option value="600">10分钟</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="alert-level" class="form-label">告警级别</label>
                    <select class="form-control" id="alert-level">
                        <option value="fail" selected>仅失败</option>
                        <option value="warning">警告及以上</option>
                        <option value="all">所有状态</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">监控状态</label>
                    <div>
                        <span id="monitor-status" class="badge bg-secondary">未启动</span>
                        <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="stopAutoMonitor()" id="stop-monitor-btn" style="display: none;">
                            停止监控
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 历史记录 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-history"></i> 检查历史</h6>
    </div>
    <div class="card-body">
        <div id="check-history">
            <p class="text-muted">暂无检查记录</p>
        </div>
    </div>
</div>

<style>
.status-indicator {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.status-pass {
    background-color: #d4edda;
    color: #155724;
    border: 2px solid #c3e6cb;
}

.status-fail {
    background-color: #f8d7da;
    color: #721c24;
    border: 2px solid #f5c6cb;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 2px solid #ffeaa7;
}

.check-item {
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid;
}

.check-pass {
    background-color: #d4edda;
    border-left-color: #28a745;
}

.check-fail {
    background-color: #f8d7da;
    border-left-color: #dc3545;
}

.check-warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
}
</style>

<script>
let monitorInterval = null;
let checkHistory = [];

// 页面加载时获取实时状态
document.addEventListener('DOMContentLoaded', function() {
    updateRealtimeStatus();
    setInterval(updateRealtimeStatus, 30000); // 每30秒更新一次
});

// 更新实时状态
function updateRealtimeStatus() {
    fetch('index.php?controller=health&action=apiQuickCheck')
        .then(response => response.json())
        .then(data => {
            const statusDiv = document.getElementById('realtime-status');
            const dbStatus = data.data.database === 'pass' ? '正常' : '异常';
            const tableStatus = data.data.tables === 'pass' ? '正常' : '异常';
            
            statusDiv.innerHTML = `
                <div class="row text-center">
                    <div class="col-6">
                        <span class="badge bg-${data.data.database === 'pass' ? 'success' : 'danger'}">
                            数据库: ${dbStatus}
                        </span>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-${data.data.tables === 'pass' ? 'success' : 'danger'}">
                            数据表: ${tableStatus}
                        </span>
                    </div>
                </div>
                <small class="text-muted d-block mt-2">最后更新: ${data.timestamp}</small>
            `;
        })
        .catch(error => {
            document.getElementById('realtime-status').innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>无法获取状态信息</p>
                </div>
            `;
        });
}

// 运行快速检查
function runQuickCheck() {
    const resultsDiv = document.getElementById('check-results');
    resultsDiv.style.display = 'block';
    resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>正在进行快速检查...</p></div>';
    
    fetch('index.php?controller=health&action=apiQuickCheck')
        .then(response => response.json())
        .then(data => {
            displayQuickResults(data.data);
            addToHistory('快速检查', data.data);
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">检查失败: ' + error.message + '</div>';
        });
}

// 运行完整检查
function runFullCheck() {
    const resultsDiv = document.getElementById('check-results');
    resultsDiv.style.display = 'block';
    resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>正在进行完整检查，请稍候...</p></div>';
    
    fetch('index.php?controller=health&action=apiFullCheck')
        .then(response => response.json())
        .then(data => {
            displayFullResults(data.data);
            addToHistory('完整检查', data.data);
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">检查失败: ' + error.message + '</div>';
        });
}

// 显示快速检查结果
function displayQuickResults(data) {
    const resultsDiv = document.getElementById('check-results');
    let html = '<div class="card"><div class="card-header"><h6>快速检查结果</h6></div><div class="card-body">';
    
    for (const [key, status] of Object.entries(data)) {
        const statusClass = status === 'pass' ? 'check-pass' : 'check-fail';
        const statusText = status === 'pass' ? '正常' : '异常';
        const icon = status === 'pass' ? 'check-circle' : 'times-circle';
        
        html += `
            <div class="check-item ${statusClass}">
                <i class="fas fa-${icon}"></i>
                <strong>${key}:</strong> ${statusText}
            </div>
        `;
    }
    
    html += '</div></div>';
    resultsDiv.innerHTML = html;
}

// 显示完整检查结果
function displayFullResults(report) {
    window.location.href = 'index.php?controller=health&action=fullCheck';
}

// 开始自动监控
function startAutoMonitor() {
    const interval = parseInt(document.getElementById('monitor-interval').value) * 1000;
    
    if (monitorInterval) {
        clearInterval(monitorInterval);
    }
    
    monitorInterval = setInterval(runQuickCheck, interval);
    
    document.getElementById('monitor-status').className = 'badge bg-success';
    document.getElementById('monitor-status').textContent = '监控中';
    document.getElementById('stop-monitor-btn').style.display = 'inline-block';
    
    // 立即执行一次检查
    runQuickCheck();
}

// 停止自动监控
function stopAutoMonitor() {
    if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
    }
    
    document.getElementById('monitor-status').className = 'badge bg-secondary';
    document.getElementById('monitor-status').textContent = '未启动';
    document.getElementById('stop-monitor-btn').style.display = 'none';
}

// 添加到历史记录
function addToHistory(type, data) {
    const timestamp = new Date().toLocaleString();
    checkHistory.unshift({
        type: type,
        timestamp: timestamp,
        data: data
    });
    
    // 只保留最近10条记录
    if (checkHistory.length > 10) {
        checkHistory = checkHistory.slice(0, 10);
    }
    
    updateHistoryDisplay();
}

// 更新历史记录显示
function updateHistoryDisplay() {
    const historyDiv = document.getElementById('check-history');
    
    if (checkHistory.length === 0) {
        historyDiv.innerHTML = '<p class="text-muted">暂无检查记录</p>';
        return;
    }
    
    let html = '<div class="list-group">';
    checkHistory.forEach(record => {
        const overallStatus = typeof record.data === 'object' && record.data.overall_status 
            ? record.data.overall_status 
            : (Object.values(record.data).includes('fail') ? 'fail' : 'pass');
        
        const badgeClass = overallStatus === 'pass' ? 'bg-success' : 'bg-danger';
        const statusText = overallStatus === 'pass' ? '正常' : '异常';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${record.type}</strong>
                        <small class="text-muted d-block">${record.timestamp}</small>
                    </div>
                    <span class="badge ${badgeClass}">${statusText}</span>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    historyDiv.innerHTML = html;
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
