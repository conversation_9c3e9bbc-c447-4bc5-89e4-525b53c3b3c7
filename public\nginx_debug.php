<?php
/**
 * Nginx环境调试
 */

session_start();

echo "<h2>Nginx + PHPStudy 环境调试</h2>";

echo "<h3>服务器环境信息</h3>";
echo "Web服务器: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "<br>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "SAPI: " . php_sapi_name() . "<br>";
echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "脚本路径: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";

echo "<h3>请求信息</h3>";
echo "请求方法: " . $_SERVER['REQUEST_METHOD'] . "<br>";
echo "请求URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "查询字符串: " . ($_SERVER['QUERY_STRING'] ?? '无') . "<br>";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "HTTPS: " . (isset($_SERVER['HTTPS']) ? 'Yes' : 'No') . "<br>";

echo "<h3>重定向相关头信息</h3>";
$headers = getallheaders();
foreach ($headers as $name => $value) {
    if (stripos($name, 'redirect') !== false || 
        stripos($name, 'location') !== false ||
        stripos($name, 'referer') !== false) {
        echo $name . ": " . $value . "<br>";
    }
}

echo "<h3>会话信息</h3>";
echo "会话ID: " . session_id() . "<br>";
echo "会话状态: " . session_status() . "<br>";
echo "会话保存路径: " . session_save_path() . "<br>";
echo "用户登录状态: " . (isset($_SESSION['user_id']) ? '已登录 (ID: ' . $_SESSION['user_id'] . ')' : '未登录') . "<br>";

echo "<h3>URL重写测试</h3>";
$testUrls = [
    'index.php',
    'index.php?controller=home&action=index',
    'index.php?controller=material&action=index',
    'materials.php'
];

foreach ($testUrls as $url) {
    echo "测试URL: <a href='{$url}' target='_blank'>{$url}</a><br>";
}

echo "<h3>文件权限检查</h3>";
$files = [
    '../app/controllers/MaterialController.php',
    '../app/views/materials/index.php',
    '../app/core/Controller.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo $file . ": 存在，权限 " . substr(sprintf('%o', $perms), -4) . "<br>";
    } else {
        echo $file . ": 不存在<br>";
    }
}

echo "<h3>PHP配置检查</h3>";
$configs = [
    'max_execution_time',
    'memory_limit',
    'error_reporting',
    'display_errors',
    'log_errors',
    'error_log',
    'session.save_handler',
    'session.save_path'
];

foreach ($configs as $config) {
    echo $config . ": " . ini_get($config) . "<br>";
}

echo "<h3>重定向循环测试</h3>";
echo "现在我们来测试重定向循环的具体原因：<br>";

// 模拟MaterialController的认证检查
function testAuth() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

echo "认证状态: " . (testAuth() ? '通过' : '失败') . "<br>";

if (testAuth()) {
    echo "✅ 用户已认证，理论上不应该重定向<br>";
    echo "问题可能在于：<br>";
    echo "1. MaterialController的构造函数或index方法有问题<br>";
    echo "2. 视图渲染过程中有重定向<br>";
    echo "3. Nginx配置导致的URL重写循环<br>";
} else {
    echo "❌ 用户未认证，会被重定向到登录页面<br>";
}

echo "<h3>Nginx配置建议</h3>";
echo "请检查PHPStudy中的Nginx配置文件，确保没有导致循环的重写规则<br>";
echo "配置文件通常在: D:\\phpstudy_pro\\Extensions\\Nginx1.15.11\\conf\\<br>";

echo "<h3>解决方案</h3>";
echo "1. <a href='materials.php'>使用独立的原材料管理系统</a> (推荐)<br>";
echo "2. <a href='view_logs.php'>查看详细日志</a><br>";
echo "3. 检查Nginx配置文件<br>";
echo "4. 重启PHPStudy服务<br>";

echo "<h3>临时解决方案</h3>";
echo "如果原版MaterialController确实有问题，我们可以：<br>";
echo "- 使用独立的页面系统（已实现）<br>";
echo "- 修改首页导航链接指向独立页面<br>";
echo "- 继续开发其他功能模块<br>";
?>
