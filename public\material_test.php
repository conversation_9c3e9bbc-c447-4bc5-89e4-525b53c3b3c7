<?php
/**
 * 原材料控制器测试
 */

session_start();

echo "<h2>MaterialController 测试</h2>";

// 检查登录状态
if (!isset($_SESSION['user_id'])) {
    echo "❌ 用户未登录<br>";
    exit;
}

echo "✅ 用户已登录: " . $_SESSION['user_id'] . "<br>";

// 模拟MaterialController的index方法
try {
    echo "<h3>步骤1: 加载必要文件</h3>";
    
    require_once '../app/core/Database.php';
    echo "✅ Database.php 加载成功<br>";
    
    require_once '../app/models/Material.php';
    echo "✅ Material.php 加载成功<br>";
    
    echo "<h3>步骤2: 实例化Material模型</h3>";
    $materialModel = new Material();
    echo "✅ Material模型实例化成功<br>";
    
    echo "<h3>步骤3: 获取数据</h3>";
    $materials = $materialModel->getAll();
    echo "✅ 获取原材料列表成功，共 " . count($materials) . " 条<br>";
    
    $stats = $materialModel->getStats();
    echo "✅ 获取统计信息成功<br>";
    
    echo "<h3>步骤4: 测试视图文件</h3>";
    $viewFile = '../app/views/materials/index.php';
    if (file_exists($viewFile)) {
        echo "✅ 视图文件存在<br>";
        
        // 设置视图需要的变量
        $search = '';
        $title = '原材料管理';
        
        echo "<h3>步骤5: 包含视图文件</h3>";
        echo "开始包含视图文件...<br>";
        
        // 使用输出缓冲来捕获可能的错误
        ob_start();
        include $viewFile;
        $output = ob_get_contents();
        ob_end_clean();
        
        echo "✅ 视图文件包含成功<br>";
        echo "输出长度: " . strlen($output) . " 字符<br>";
        
        // 检查输出中是否有重定向
        if (strpos($output, 'Location:') !== false) {
            echo "❌ 视图文件中包含重定向<br>";
        } else {
            echo "✅ 视图文件中没有重定向<br>";
        }
        
    } else {
        echo "❌ 视图文件不存在<br>";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
}

echo "<h3>结论</h3>";
echo "如果以上所有步骤都成功，问题可能在于：<br>";
echo "1. MaterialController的构造函数或其他方法<br>";
echo "2. 视图文件中的某些逻辑<br>";
echo "3. PHP配置问题<br>";

echo "<h3>下一步测试</h3>";
echo '<a href="material_direct.php">直接测试MaterialController</a><br>';
?>
