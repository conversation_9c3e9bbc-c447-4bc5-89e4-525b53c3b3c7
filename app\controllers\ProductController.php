<?php
/**
 * 成品控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Product.php';

class ProductController extends Controller
{
    private $productModel;

    public function __construct()
    {
        parent::__construct();
        $this->requireAuth();
        $this->productModel = new Product();
    }

    /**
     * 成品列表页
     */
    public function index()
    {
        $search = $_GET['search'] ?? '';
        
        if (!empty($search)) {
            $products = $this->productModel->search($search);
        } else {
            $products = $this->productModel->getAll();
        }
        
        $stats = $this->productModel->getStats();
        
        $this->view('products/index', [
            'title' => '成品管理',
            'products' => $products,
            'stats' => $stats,
            'search' => $search
        ]);
    }

    /**
     * 显示新增成品页面
     */
    public function create()
    {
        $this->view('products/create', [
            'title' => '添加成品',
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理新增成品请求
     */
    public function store()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=product&action=index');
        }

        $this->validateCsrfToken();

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '成品名称不能为空且不超过100个字符'
            ],
            'unit' => [
                'required' => true,
                'max_length' => 20,
                'message' => '单位不能为空且不超过20个字符'
            ],
            'selling_price' => [
                'required' => true,
                'message' => '销售价格不能为空'
            ]
        ]);

        // 验证价格
        if (empty($errors) && (!is_numeric($_POST['selling_price']) || floatval($_POST['selling_price']) <= 0)) {
            $errors['selling_price'] = '销售价格必须大于0';
        }

        // 检查名称是否已存在
        if (empty($errors) && $this->productModel->nameExists($_POST['name'])) {
            $errors['name'] = '该成品名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=product&action=create');
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'specification' => trim($_POST['specification'] ?? ''),
                'unit' => trim($_POST['unit']),
                'stock_quantity' => floatval($_POST['stock_quantity'] ?? 0),
                'selling_price' => floatval($_POST['selling_price'])
            ];

            $id = $this->productModel->create($data);
            
            $this->setFlash('success', '成品添加成功');
            $this->redirect('index.php?controller=product&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '添加失败：' . $e->getMessage());
            $this->redirect('index.php?controller=product&action=create');
        }
    }

    /**
     * 显示编辑成品页面
     */
    public function edit()
    {
        $id = $_GET['id'] ?? 0;
        $product = $this->productModel->getById($id);
        
        if (!$product) {
            $this->setFlash('error', '成品不存在');
            $this->redirect('index.php?controller=product&action=index');
        }

        $this->view('products/edit', [
            'title' => '编辑成品',
            'product' => $product,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理更新成品请求
     */
    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=product&action=index');
        }

        $this->validateCsrfToken();

        $id = $_POST['id'] ?? 0;
        $product = $this->productModel->getById($id);
        
        if (!$product) {
            $this->setFlash('error', '成品不存在');
            $this->redirect('index.php?controller=product&action=index');
        }

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '成品名称不能为空且不超过100个字符'
            ],
            'unit' => [
                'required' => true,
                'max_length' => 20,
                'message' => '单位不能为空且不超过20个字符'
            ],
            'selling_price' => [
                'required' => true,
                'message' => '销售价格不能为空'
            ]
        ]);

        // 验证价格
        if (empty($errors) && (!is_numeric($_POST['selling_price']) || floatval($_POST['selling_price']) <= 0)) {
            $errors['selling_price'] = '销售价格必须大于0';
        }

        // 检查名称是否已存在（排除当前记录）
        if (empty($errors) && $this->productModel->nameExists($_POST['name'], $id)) {
            $errors['name'] = '该成品名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=product&action=edit&id=' . $id);
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'specification' => trim($_POST['specification'] ?? ''),
                'unit' => trim($_POST['unit']),
                'stock_quantity' => floatval($_POST['stock_quantity'] ?? 0),
                'selling_price' => floatval($_POST['selling_price'])
            ];

            $this->productModel->update($id, $data);
            
            $this->setFlash('success', '成品更新成功');
            $this->redirect('index.php?controller=product&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '更新失败：' . $e->getMessage());
            $this->redirect('index.php?controller=product&action=edit&id=' . $id);
        }
    }

    /**
     * 删除成品
     */
    public function delete()
    {
        $id = $_GET['id'] ?? 0;
        $product = $this->productModel->getById($id);
        
        if (!$product) {
            $this->setFlash('error', '成品不存在');
            $this->redirect('index.php?controller=product&action=index');
        }

        try {
            $this->productModel->delete($id);
            $this->setFlash('success', '成品删除成功');
        } catch (Exception $e) {
            $this->setFlash('error', '删除失败：' . $e->getMessage());
        }
        
        $this->redirect('index.php?controller=product&action=index');
    }

    /**
     * 查看成品详情
     */
    public function view()
    {
        $id = $_GET['id'] ?? 0;
        $product = $this->productModel->getWithRecipe($id);
        
        if (!$product) {
            $this->setFlash('error', '成品不存在');
            $this->redirect('index.php?controller=product&action=index');
        }

        $this->view('products/view', [
            'title' => '成品详情',
            'product' => $product
        ]);
    }
}
