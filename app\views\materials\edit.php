<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-edit text-primary"></i> 编辑原材料</h2>
        <p class="text-muted mb-0">修改原材料信息</p>
    </div>
    <div>
        <a href="index.php?controller=material&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 编辑表单 -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cube"></i> 编辑：<?php echo htmlspecialchars($material['name']); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=material&action=update">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="id" value="<?php echo $material['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i> 原材料名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="例如：糯米粉" maxlength="100"
                                       value="<?php echo htmlspecialchars($material['name']); ?>">
                                <div class="form-text">原材料的名称，必填项</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="specification" class="form-label">
                                    <i class="fas fa-info-circle"></i> 规格说明
                                </label>
                                <input type="text" class="form-control" id="specification" name="specification" 
                                       placeholder="例如：每袋500g" maxlength="100"
                                       value="<?php echo htmlspecialchars($material['specification'] ?? ''); ?>">
                                <div class="form-text">规格描述，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit" class="form-label">
                                    <i class="fas fa-balance-scale"></i> 单位 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="unit" name="unit" required>
                                    <option value="">请选择单位</option>
                                    <?php 
                                    $units = ['袋', '包', '盒', '瓶', '罐', 'kg', 'g', 'L', 'ml', '个'];
                                    foreach ($units as $unit): 
                                    ?>
                                        <option value="<?php echo $unit; ?>" 
                                                <?php echo $material['unit'] === $unit ? 'selected' : ''; ?>>
                                            <?php echo $unit; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">计量单位，必填项</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label">
                                    <i class="fas fa-warehouse"></i> 库存数量
                                </label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       min="0" step="0.001" placeholder="0.000"
                                       value="<?php echo $material['stock_quantity']; ?>">
                                <div class="form-text">当前库存数量</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit_price" class="form-label">
                                    <i class="fas fa-money-bill"></i> 单价
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                           min="0" step="0.01" placeholder="0.00"
                                           value="<?php echo $material['unit_price'] ?? ''; ?>">
                                </div>
                                <div class="form-text">每单位的价格，用于成本计算，可选</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-calculator"></i> 库存价值
                                </label>
                                <div class="form-control-plaintext" id="stock_value">
                                    <?php 
                                    if ($material['unit_price']) {
                                        echo '¥' . number_format($material['stock_quantity'] * $material['unit_price'], 2);
                                    } else {
                                        echo '未设置单价';
                                    }
                                    ?>
                                </div>
                                <div class="form-text">库存数量 × 单价</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 创建时间信息 -->
                    <div class="alert alert-light">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            创建时间：<?php echo date('Y-m-d H:i:s', strtotime($material['created_at'])); ?>
                            <?php if ($material['updated_at'] !== $material['created_at']): ?>
                                <br>
                                <i class="fas fa-edit"></i> 
                                最后更新：<?php echo date('Y-m-d H:i:s', strtotime($material['updated_at'])); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=material&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 自动计算库存价值
    $('#stock_quantity, #unit_price').on('input', function() {
        var quantity = parseFloat($('#stock_quantity').val()) || 0;
        var price = parseFloat($('#unit_price').val()) || 0;
        var value = quantity * price;
        
        if (price > 0) {
            $('#stock_value').text('¥' + value.toFixed(2));
        } else {
            $('#stock_value').text('未设置单价');
        }
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
