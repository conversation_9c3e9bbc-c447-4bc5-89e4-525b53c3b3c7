<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-eye text-primary"></i> 销售详情</h2>
        <p class="text-muted mb-0">销售单 #<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?></p>
    </div>
    <div>
        <a href="index.php?controller=sale&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        <a href="index.php?controller=sale&action=print&id=<?php echo $sale['id']; ?>" 
           class="btn btn-outline-primary" target="_blank">
            <i class="fas fa-print"></i> 打印
        </a>
    </div>
</div>

<div class="row">
    <!-- 销售基本信息 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>销售单号:</strong></td>
                        <td>#<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?></td>
                    </tr>
                    <tr>
                        <td><strong>客户:</strong></td>
                        <td>
                            <?php if ($sale['customer_name']): ?>
                                <a href="index.php?controller=customer&action=detail&id=<?php echo $sale['customer_id']; ?>" 
                                   class="text-decoration-none">
                                    <?php echo htmlspecialchars($sale['customer_name']); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">散客</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if ($sale['customer_contact']): ?>
                    <tr>
                        <td><strong>联系方式:</strong></td>
                        <td><?php echo htmlspecialchars($sale['customer_contact']); ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td><strong>销售时间:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($sale['created_at'])); ?></td>
                    </tr>
                    <?php if (!empty($sale['notes'])): ?>
                    <tr>
                        <td><strong>备注:</strong></td>
                        <td><?php echo nl2br(htmlspecialchars($sale['notes'])); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 金额信息 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> 金额信息
                </h5>
            </div>
            <div class="card-body">
                <?php $debt = $sale['total_amount'] - $sale['payment_amount']; ?>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>商品总额:</strong></td>
                        <td class="text-end">¥<?php echo number_format($sale['total_amount'], 2); ?></td>
                    </tr>
                    <tr>
                        <td><strong>收款金额:</strong></td>
                        <td class="text-end">¥<?php echo number_format($sale['payment_amount'], 2); ?></td>
                    </tr>
                    <tr class="border-top">
                        <td><strong>欠款金额:</strong></td>
                        <td class="text-end">
                            <span class="<?php echo $debt > 0 ? 'text-danger fw-bold' : 'text-success'; ?>">
                                ¥<?php echo number_format($debt, 2); ?>
                            </span>
                        </td>
                    </tr>
                </table>
                
                <?php if ($debt > 0): ?>
                    <div class="alert alert-warning mt-3">
                        <small>
                            <i class="fas fa-exclamation-triangle"></i> 
                            客户还有 ¥<?php echo number_format($debt, 2); ?> 未付款
                        </small>
                    </div>
                <?php elseif ($debt < 0): ?>
                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-info-circle"></i> 
                            客户多付了 ¥<?php echo number_format(abs($debt), 2); ?>
                        </small>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success mt-3">
                        <small>
                            <i class="fas fa-check-circle"></i> 
                            款项已结清
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 销售商品明细 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-box"></i> 销售商品明细
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($sale['items'])): ?>
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <h6 class="text-muted">该销售单暂无商品明细</h6>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>商品名称</th>
                            <th>单位</th>
                            <th>数量</th>
                            <th>单价</th>
                            <th>小计</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sale['items'] as $item): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($item['product_unit']); ?></td>
                                <td><?php echo number_format($item['quantity'], 3); ?></td>
                                <td>¥<?php echo number_format($item['unit_price'], 2); ?></td>
                                <td>¥<?php echo number_format($item['quantity'] * $item['unit_price'], 2); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <td colspan="4"><strong>合计</strong></td>
                            <td><strong>¥<?php echo number_format($sale['total_amount'], 2); ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 操作按钮 -->
<div class="card mt-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted">相关操作</h6>
                <div class="d-grid gap-2 d-md-flex">
                    <a href="index.php?controller=sale&action=create&customer_id=<?php echo $sale['customer_id']; ?>" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> 为此客户再次开单
                    </a>
                    <?php if ($sale['customer_id']): ?>
                        <a href="index.php?controller=customer&action=detail&id=<?php echo $sale['customer_id']; ?>" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-user"></i> 查看客户详情
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-6">
                <h6 class="text-muted">危险操作</h6>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?controller=sale&action=delete&id=<?php echo $sale['id']; ?>" 
                       class="btn btn-outline-danger btn-delete"
                       data-item-name="销售单 #<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?>">
                        <i class="fas fa-trash"></i> 删除销售单
                    </a>
                </div>
                <small class="text-muted">删除后将恢复库存和客户余额</small>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
