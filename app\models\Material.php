<?php
/**
 * 原材料模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class Material
{
    private $db;
    private $table = 'materials';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取所有原材料
     */
    public function getAll($orderBy = 'name', $order = 'ASC')
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY {$orderBy} {$order}";
        return $this->db->fetchAll($sql);
    }

    /**
     * 根据ID获取原材料
     */
    public function getById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? LIMIT 1";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 创建新原材料
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (name, specification, unit, stock_quantity, unit_price) VALUES (?, ?, ?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['specification'] ?? null,
            $data['unit'],
            $data['stock_quantity'] ?? 0,
            $data['unit_price'] ?? null
        ];
        
        $this->db->execute($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * 更新原材料
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [];
        
        if (isset($data['name'])) {
            $fields[] = 'name = ?';
            $params[] = $data['name'];
        }
        
        if (isset($data['specification'])) {
            $fields[] = 'specification = ?';
            $params[] = $data['specification'];
        }
        
        if (isset($data['unit'])) {
            $fields[] = 'unit = ?';
            $params[] = $data['unit'];
        }
        
        if (isset($data['stock_quantity'])) {
            $fields[] = 'stock_quantity = ?';
            $params[] = $data['stock_quantity'];
        }
        
        if (isset($data['unit_price'])) {
            $fields[] = 'unit_price = ?';
            $params[] = $data['unit_price'];
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        return $this->db->execute($sql, $params) > 0;
    }

    /**
     * 删除原材料
     */
    public function delete($id)
    {
        // 检查是否被配方使用
        $inUse = $this->db->fetch("SELECT COUNT(*) as count FROM recipe_items WHERE material_id = ?", [$id]);
        if ($inUse['count'] > 0) {
            throw new Exception('该原材料正在被配方使用，无法删除');
        }
        
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->execute($sql, [$id]) > 0;
    }

    /**
     * 检查名称是否已存在
     */
    public function nameExists($name, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }

    /**
     * 更新库存数量
     */
    public function updateStock($id, $quantity, $operation = 'set')
    {
        if ($operation === 'add') {
            $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity + ? WHERE id = ?";
        } elseif ($operation === 'subtract') {
            $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity - ? WHERE id = ?";
        } else {
            $sql = "UPDATE {$this->table} SET stock_quantity = ? WHERE id = ?";
        }
        
        return $this->db->execute($sql, [$quantity, $id]) > 0;
    }

    /**
     * 获取库存不足的原材料
     */
    public function getLowStock($threshold = 5)
    {
        $sql = "SELECT * FROM {$this->table} WHERE stock_quantity < ? ORDER BY stock_quantity ASC";
        return $this->db->fetchAll($sql, [$threshold]);
    }

    /**
     * 搜索原材料
     */
    public function search($keyword)
    {
        $sql = "SELECT * FROM {$this->table} WHERE name LIKE ? OR specification LIKE ? ORDER BY name";
        $searchTerm = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    /**
     * 获取统计信息
     */
    public function getStats()
    {
        $stats = [];
        
        // 总数量
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total_count'] = $result['count'];
        
        // 库存不足数量
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table} WHERE stock_quantity < 5");
        $stats['low_stock_count'] = $result['count'];
        
        // 总库存价值
        $result = $this->db->fetch("SELECT SUM(stock_quantity * IFNULL(unit_price, 0)) as total_value FROM {$this->table}");
        $stats['total_value'] = $result['total_value'] ?? 0;
        
        return $stats;
    }
}
