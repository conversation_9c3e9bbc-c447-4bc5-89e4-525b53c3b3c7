<?php
/**
 * 系统健康检查
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '权限不足']);
    exit;
}

require_once '../../../app/core/SystemHealthChecker.php';

header('Content-Type: application/json');

try {
    $healthChecker = new SystemHealthChecker();
    $report = $healthChecker->runFullCheck();
    
    // 保存检查结果到调试目录
    $debugDir = __DIR__ . '/..';
    if (!is_dir($debugDir . '/logs')) {
        mkdir($debugDir . '/logs', 0755, true);
    }
    
    $logFile = $debugDir . '/logs/health_check_' . date('Y-m-d_H-i-s') . '.json';
    file_put_contents($logFile, json_encode($report, JSON_PRETTY_PRINT));
    
    echo json_encode([
        'success' => true,
        'message' => '系统健康检查完成',
        'details' => $report,
        'log_file' => basename($logFile)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '健康检查失败: ' . $e->getMessage()
    ]);
}
?>
