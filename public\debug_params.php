<?php
session_start();

echo "<h2>参数调试</h2>";

echo "<h3>GET参数</h3>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

echo "<h3>POST参数</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

echo "<h3>REQUEST_METHOD</h3>";
echo $_SERVER['REQUEST_METHOD'] ?? 'undefined';

echo "<h3>REQUEST_URI</h3>";
echo $_SERVER['REQUEST_URI'] ?? 'undefined';

echo "<h3>会话Flash消息</h3>";
echo "<pre>";
print_r($_SESSION['flash'] ?? []);
echo "</pre>";

// 清除flash消息
if (isset($_SESSION['flash'])) {
    unset($_SESSION['flash']);
    echo "<p>✅ Flash消息已清除</p>";
}

echo "<h3>测试链接</h3>";
echo '<a href="index.php?controller=material&action=index">原材料管理（清除flash后）</a><br>';
echo '<a href="quick_fix.php">快速修复版</a><br>';
?>
