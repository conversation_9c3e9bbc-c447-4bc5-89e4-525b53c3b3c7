<?php
session_start();
echo "<h2>系统测试</h2>";

echo "<h3>会话状态</h3>";
if (isset($_SESSION['user_id'])) {
    echo "已登录，用户ID: " . $_SESSION['user_id'] . "<br>";
    echo "用户名: " . ($_SESSION['username'] ?? '未设置') . "<br>";
    echo "姓名: " . ($_SESSION['name'] ?? '未设置') . "<br>";
} else {
    echo "未登录<br>";
}

echo "<h3>会话详情</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>PHP信息</h3>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "会话ID: " . session_id() . "<br>";

echo "<h3>文件检查</h3>";
$files = [
    '../app/controllers/MaterialController.php',
    '../app/models/Material.php',
    '../app/views/materials/index.php'
];

foreach ($files as $file) {
    echo $file . ": " . (file_exists($file) ? "存在" : "不存在") . "<br>";
}

echo "<h3>数据库测试</h3>";
try {
    require_once '../app/core/Database.php';
    $db = Database::getInstance();
    echo "数据库连接: 成功<br>";

    // 检查用户表
    $result = $db->fetch("SELECT COUNT(*) as count FROM users");
    echo "用户表记录数: " . $result['count'] . "<br>";

} catch (Exception $e) {
    echo "数据库连接: 失败 - " . $e->getMessage() . "<br>";
}

echo "<h3>测试链接</h3>";
echo '<a href="index.php">首页</a><br>';
echo '<a href="index.php?controller=auth&action=login">登录</a><br>';

if (isset($_SESSION['user_id'])) {
    echo '<a href="index.php?controller=material&action=index">原材料管理</a><br>';
} else {
    echo '<span style="color:red;">请先登录后再访问原材料管理</span><br>';
}
?>
