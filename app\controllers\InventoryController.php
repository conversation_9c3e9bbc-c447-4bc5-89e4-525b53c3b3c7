<?php
/**
 * 库存管理控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Material.php';
require_once __DIR__ . '/../models/Product.php';
require_once __DIR__ . '/../models/InventoryLog.php';

class InventoryController extends Controller
{
    private $materialModel;
    private $productModel;
    private $inventoryLogModel;

    public function __construct()
    {
        parent::__construct();
        $this->materialModel = new Material();
        $this->productModel = new Product();
        $this->inventoryLogModel = new InventoryLog();
    }

    /**
     * 库存总览页面
     */
    public function index()
    {
        $this->requireAuth();

        // 获取库存统计
        $materialStats = $this->materialModel->getStats();
        $productStats = $this->productModel->getStats();

        // 获取低库存物品
        $lowStockMaterials = $this->materialModel->getLowStock(10);
        $lowStockProducts = $this->productModel->getLowStock(5);

        // 获取最近的库存变动
        $recentLogs = $this->inventoryLogModel->getAll([], 20);

        $this->view('inventory/index', [
            'title' => '库存管理',
            'materialStats' => $materialStats,
            'productStats' => $productStats,
            'lowStockMaterials' => $lowStockMaterials,
            'lowStockProducts' => $lowStockProducts,
            'recentLogs' => $recentLogs
        ]);
    }

    /**
     * 库存调整页面
     */
    public function adjust()
    {
        $this->requireAuth();

        $itemType = $_GET['type'] ?? 'material';
        $itemId = $_GET['id'] ?? 0;

        if ($itemType === 'material') {
            $item = $this->materialModel->getById($itemId);
            $items = $this->materialModel->getAll();
        } else {
            $item = $this->productModel->getById($itemId);
            $items = $this->productModel->getAll();
        }

        if (!$item && $itemId > 0) {
            $this->setFlash('error', '物品不存在');
            $this->redirect('index.php?controller=inventory&action=index');
        }

        $this->view('inventory/adjust', [
            'title' => '库存调整',
            'itemType' => $itemType,
            'item' => $item,
            'items' => $items,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理库存调整
     */
    public function doAdjust()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=inventory&action=index');
        }

        $this->validateCsrfToken();

        $itemType = $_POST['item_type'] ?? '';
        $itemId = $_POST['item_id'] ?? 0;
        $adjustType = $_POST['adjust_type'] ?? 'set'; // set, add, subtract
        $quantity = floatval($_POST['quantity'] ?? 0);
        $reason = trim($_POST['reason'] ?? '');

        // 验证输入
        if (empty($itemType) || empty($itemId)) {
            $this->setFlash('error', '请选择要调整的物品');
            $this->redirect('index.php?controller=inventory&action=adjust');
        }

        if ($quantity <= 0 && $adjustType !== 'set') {
            $this->setFlash('error', '调整数量必须大于0');
            $this->redirect('index.php?controller=inventory&action=adjust&type=' . $itemType . '&id=' . $itemId);
        }

        try {
            // 获取当前库存
            if ($itemType === 'material') {
                $item = $this->materialModel->getById($itemId);
                $model = $this->materialModel;
            } else {
                $item = $this->productModel->getById($itemId);
                $model = $this->productModel;
            }

            if (!$item) {
                throw new Exception('物品不存在');
            }

            $currentStock = $item['stock_quantity'];
            $newStock = $currentStock;

            // 计算新库存
            switch ($adjustType) {
                case 'set':
                    $newStock = $quantity;
                    $change = $quantity - $currentStock;
                    break;
                case 'add':
                    $newStock = $currentStock + $quantity;
                    $change = $quantity;
                    break;
                case 'subtract':
                    $newStock = $currentStock - $quantity;
                    $change = -$quantity;
                    break;
                default:
                    throw new Exception('无效的调整类型');
            }

            if ($newStock < 0) {
                throw new Exception('调整后库存不能为负数');
            }

            // 更新库存
            $model->updateStock($itemId, $newStock, 'set');

            // 记录库存变动
            $this->inventoryLogModel->log(
                $itemType,
                $itemId,
                'adjust',
                $currentStock,
                $change,
                $newStock,
                [
                    'reason' => $reason ?: '手动调整',
                    'unit_price' => $itemType === 'material' ? $item['unit_price'] : $item['selling_price'],
                    'total_value' => abs($change) * ($itemType === 'material' ? $item['unit_price'] : $item['selling_price'])
                ]
            );

            $this->setFlash('success', '库存调整成功');
            $this->redirect('index.php?controller=inventory&action=index');

        } catch (Exception $e) {
            $this->setFlash('error', '调整失败：' . $e->getMessage());
            $this->redirect('index.php?controller=inventory&action=adjust&type=' . $itemType . '&id=' . $itemId);
        }
    }

    /**
     * 库存变动记录
     */
    public function logs()
    {
        $this->requireAuth();

        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = 50;
        $offset = ($page - 1) * $limit;

        // 筛选条件
        $filters = [];
        if (!empty($_GET['item_type'])) {
            $filters['item_type'] = $_GET['item_type'];
        }
        if (!empty($_GET['change_type'])) {
            $filters['change_type'] = $_GET['change_type'];
        }
        if (!empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        if (!empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }

        $logs = $this->inventoryLogModel->getAll($filters, $limit, $offset);
        $stats = $this->inventoryLogModel->getStats($filters['start_date'] ?? null, $filters['end_date'] ?? null);

        $this->view('inventory/logs', [
            'title' => '库存变动记录',
            'logs' => $logs,
            'stats' => $stats,
            'filters' => $filters,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 库存报表
     */
    public function reports()
    {
        $this->requireAuth();

        $reportType = $_GET['type'] ?? 'overview';

        switch ($reportType) {
            case 'overview':
                $this->reportOverview();
                break;
            case 'value':
                $this->reportValue();
                break;
            case 'turnover':
                $this->reportTurnover();
                break;
            default:
                $this->reportOverview();
        }
    }

    /**
     * 库存概览报表
     */
    private function reportOverview()
    {
        $materials = $this->materialModel->getAll();
        $products = $this->productModel->getAll();

        // 计算库存价值
        $materialValue = 0;
        foreach ($materials as &$material) {
            $value = $material['stock_quantity'] * $material['unit_price'];
            $material['stock_value'] = $value;
            $materialValue += $value;
        }

        $productValue = 0;
        foreach ($products as &$product) {
            $value = $product['stock_quantity'] * $product['selling_price'];
            $product['stock_value'] = $value;
            $productValue += $value;
        }

        $this->view('inventory/reports/overview', [
            'title' => '库存概览报表',
            'materials' => $materials,
            'products' => $products,
            'materialValue' => $materialValue,
            'productValue' => $productValue,
            'totalValue' => $materialValue + $productValue
        ]);
    }

    /**
     * 库存价值报表
     */
    private function reportValue()
    {
        // 实现库存价值分析
        $this->view('inventory/reports/value', [
            'title' => '库存价值报表'
        ]);
    }

    /**
     * 库存周转率报表
     */
    private function reportTurnover()
    {
        // 实现库存周转率分析
        $this->view('inventory/reports/turnover', [
            'title' => '库存周转率报表'
        ]);
    }
}
