<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-history"></i> <?php echo $title; ?></h2>
    <a href="index.php?controller=inventory&action=index" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回库存管理
    </a>
</div>

<!-- 筛选条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-filter"></i> 筛选条件</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="index.php">
            <input type="hidden" name="controller" value="inventory">
            <input type="hidden" name="action" value="logs">
            
            <div class="row">
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="item_type" class="form-label">物品类型</label>
                        <select class="form-control" id="item_type" name="item_type">
                            <option value="">全部</option>
                            <option value="material" <?php echo ($filters['item_type'] ?? '') === 'material' ? 'selected' : ''; ?>>原材料</option>
                            <option value="product" <?php echo ($filters['item_type'] ?? '') === 'product' ? 'selected' : ''; ?>>成品</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="change_type" class="form-label">变动类型</label>
                        <select class="form-control" id="change_type" name="change_type">
                            <option value="">全部</option>
                            <option value="in" <?php echo ($filters['change_type'] ?? '') === 'in' ? 'selected' : ''; ?>>入库</option>
                            <option value="out" <?php echo ($filters['change_type'] ?? '') === 'out' ? 'selected' : ''; ?>>出库</option>
                            <option value="adjust" <?php echo ($filters['change_type'] ?? '') === 'adjust' ? 'selected' : ''; ?>>调整</option>
                            <option value="produce" <?php echo ($filters['change_type'] ?? '') === 'produce' ? 'selected' : ''; ?>>生产</option>
                            <option value="sale" <?php echo ($filters['change_type'] ?? '') === 'sale' ? 'selected' : ''; ?>>销售</option>
                            <option value="return" <?php echo ($filters['change_type'] ?? '') === 'return' ? 'selected' : ''; ?>>退货</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="<?php echo $filters['start_date'] ?? ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="<?php echo $filters['end_date'] ?? ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 筛选
                            </button>
                            <a href="index.php?controller=inventory&action=logs" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> 清除
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 统计信息 -->
<?php if (!empty($stats['summary'])): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4><?php echo number_format($stats['summary']['total_records']); ?></h4>
                <small>总变动记录</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo number_format($stats['summary']['total_quantity_change'], 3); ?></h4>
                <small>总变动数量</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4>¥<?php echo number_format($stats['summary']['total_value_change'], 2); ?></h4>
                <small>总变动价值</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo count($stats['by_type']); ?></h4>
                <small>变动类型数</small>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- 变动记录列表 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fas fa-list"></i> 库存变动记录</h6>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-success" onclick="exportLogs()">
                    <i class="fas fa-download"></i> 导出
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($logs)): ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无库存变动记录</p>
                <a href="index.php?controller=inventory&action=adjust" class="btn btn-primary">
                    <i class="fas fa-edit"></i> 进行库存调整
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>物品</th>
                            <th>变动类型</th>
                            <th>变动前</th>
                            <th>变动数量</th>
                            <th>变动后</th>
                            <th>单价</th>
                            <th>总价值</th>
                            <th>操作员</th>
                            <th>原因</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                        <tr>
                            <td>
                                <small><?php echo date('Y-m-d', strtotime($log['created_at'])); ?></small><br>
                                <small class="text-muted"><?php echo date('H:i:s', strtotime($log['created_at'])); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $log['item_type'] === 'material' ? 'secondary' : 'primary'; ?>">
                                    <?php echo $log['item_type'] === 'material' ? '原料' : '成品'; ?>
                                </span><br>
                                <strong><?php echo htmlspecialchars($log['item_name']); ?></strong>
                            </td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo match($log['change_type']) {
                                        'in' => 'success',
                                        'out' => 'danger', 
                                        'adjust' => 'warning',
                                        'produce' => 'info',
                                        'sale' => 'dark',
                                        'return' => 'secondary',
                                        default => 'light'
                                    };
                                ?>">
                                    <?php echo \InventoryLog::getChangeTypeText($log['change_type']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo number_format($log['quantity_before'], 3); ?>
                                <small class="text-muted"><?php echo $log['item_unit']; ?></small>
                            </td>
                            <td>
                                <span class="<?php echo $log['quantity_change'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo $log['quantity_change'] > 0 ? '+' : ''; ?><?php echo number_format($log['quantity_change'], 3); ?>
                                </span>
                                <small class="text-muted"><?php echo $log['item_unit']; ?></small>
                            </td>
                            <td>
                                <?php echo number_format($log['quantity_after'], 3); ?>
                                <small class="text-muted"><?php echo $log['item_unit']; ?></small>
                            </td>
                            <td>
                                <?php if ($log['unit_price']): ?>
                                    ¥<?php echo number_format($log['unit_price'], 2); ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($log['total_value']): ?>
                                    <span class="<?php echo $log['quantity_change'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                        ¥<?php echo number_format($log['total_value'], 2); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($log['operator_name']); ?></td>
                            <td>
                                <?php if ($log['reason']): ?>
                                    <span title="<?php echo htmlspecialchars($log['reason']); ?>">
                                        <?php echo mb_substr(htmlspecialchars($log['reason']), 0, 20); ?>
                                        <?php if (mb_strlen($log['reason']) > 20): ?>...<?php endif; ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="d-flex justify-content-center mt-3">
                <nav>
                    <ul class="pagination">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?controller=inventory&action=logs&page=<?php echo $page - 1; ?><?php echo http_build_query($filters) ? '&' . http_build_query($filters) : ''; ?>">
                                    上一页
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <li class="page-item active">
                            <span class="page-link">第 <?php echo $page; ?> 页</span>
                        </li>
                        
                        <?php if (count($logs) >= $limit): ?>
                            <li class="page-item">
                                <a class="page-link" href="?controller=inventory&action=logs&page=<?php echo $page + 1; ?><?php echo http_build_query($filters) ? '&' . http_build_query($filters) : ''; ?>">
                                    下一页
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function exportLogs() {
    // 构建导出URL
    const params = new URLSearchParams(window.location.search);
    params.set('controller', 'inventory');
    params.set('action', 'exportLogs');
    
    window.open('index.php?' + params.toString(), '_blank');
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
