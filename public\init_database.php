<?php
/**
 * 数据库初始化页面
 * 创建缺失的表
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

require_once '../app/core/Database.php';

$db = Database::getInstance();

echo "<h2>数据库初始化</h2>";

try {
    // 创建销售表
    echo "<h3>创建销售表...</h3>";
    
    $salesTableSQL = "
    CREATE TABLE IF NOT EXISTS `sales` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `customer_id` int(11) DEFAULT NULL,
      `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
      `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
      `notes` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_customer_id` (`customer_id`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售记录表'
    ";
    
    $db->execute($salesTableSQL);
    echo "<p style='color: green;'>✅ 销售表创建成功</p>";
    
    // 创建销售项目表
    echo "<h3>创建销售项目表...</h3>";
    
    $saleItemsTableSQL = "
    CREATE TABLE IF NOT EXISTS `sale_items` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `sale_id` int(11) NOT NULL,
      `product_id` int(11) NOT NULL,
      `quantity` decimal(10,3) NOT NULL,
      `unit_price` decimal(10,2) NOT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_sale_id` (`sale_id`),
      KEY `idx_product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售项目表'
    ";
    
    $db->execute($saleItemsTableSQL);
    echo "<p style='color: green;'>✅ 销售项目表创建成功</p>";
    
    // 检查所有表是否存在
    echo "<h3>检查表状态...</h3>";
    
    $tables = ['materials', 'products', 'customers', 'recipes', 'recipe_items', 'sales', 'sale_items'];
    
    foreach ($tables as $table) {
        try {
            $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
            echo "<p style='color: green;'>✅ 表 $table 存在，记录数: $count</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 表 $table 不存在或有错误: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>数据库初始化完成！</h3>";
    echo "<p><a href='debug.php'>返回调试页面</a></p>";
    echo "<p><a href='index.php'>返回首页</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 初始化失败: " . $e->getMessage() . "</p>";
}
?>
