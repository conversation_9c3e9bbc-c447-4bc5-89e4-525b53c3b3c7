<?php
/**
 * 查看PHP错误日志
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

echo "<h2>PHP错误日志查看</h2>";

// 可能的日志文件路径
$logPaths = [
    'D:\phpstudy_pro\Extensions\php\php7.3.4nts\logs\php_errors.log',
    'D:\phpstudy_pro\Extensions\Nginx1.15.11\logs\error.log',
    'D:\phpstudy_pro\Extensions\Nginx1.15.11\logs\access.log',
    ini_get('error_log'),
    'C:\Windows\temp\php-errors.log',
    './error.log'
];

echo "<h3>PHP配置信息</h3>";
echo "错误日志路径: " . ini_get('error_log') . "<br>";
echo "日志错误: " . (ini_get('log_errors') ? '开启' : '关闭') . "<br>";
echo "显示错误: " . (ini_get('display_errors') ? '开启' : '关闭') . "<br>";

echo "<h3>查找日志文件</h3>";
$foundLogs = [];

foreach ($logPaths as $path) {
    if (file_exists($path)) {
        $foundLogs[] = $path;
        echo "✅ 找到日志: " . $path . "<br>";
    } else {
        echo "❌ 不存在: " . $path . "<br>";
    }
}

if (empty($foundLogs)) {
    echo "<p>未找到日志文件，请检查PHPStudy配置</p>";
} else {
    foreach ($foundLogs as $logFile) {
        echo "<h3>日志文件: " . basename($logFile) . "</h3>";
        
        if (filesize($logFile) > 1024 * 1024) {
            echo "<p>日志文件太大 (" . round(filesize($logFile) / 1024 / 1024, 2) . "MB)，只显示最后1000行</p>";
            $lines = file($logFile);
            $lines = array_slice($lines, -1000);
            $content = implode('', $lines);
        } else {
            $content = file_get_contents($logFile);
        }
        
        // 过滤包含MaterialController的行
        $lines = explode("\n", $content);
        $materialLines = [];

        foreach ($lines as $line) {
            if (stripos($line, 'material') !== false ||
                stripos($line, 'redirect') !== false ||
                stripos($line, 'controller=material') !== false ||
                stripos($line, '302') !== false ||
                stripos($line, 'too many redirects') !== false ||
                stripos($line, 'error') !== false) {
                $materialLines[] = $line;
            }
        }
        
        if (!empty($materialLines)) {
            echo "<h4>相关错误信息:</h4>";
            echo "<pre style='background:#f8f9fa; padding:10px; max-height:400px; overflow-y:scroll;'>";
            echo htmlspecialchars(implode("\n", array_slice($materialLines, -50))); // 最后50行
            echo "</pre>";
        } else {
            echo "<p>未找到相关错误信息</p>";
        }
    }
}

echo "<h3>手动触发错误测试</h3>";
echo "现在访问原材料管理页面，然后刷新此页面查看新的错误信息<br>";
echo '<a href="index.php?controller=material&action=index" target="_blank">触发MaterialController错误</a><br>';
echo '<a href="view_logs.php">刷新日志</a><br>';

echo "<h3>其他调试信息</h3>";
echo '<a href="materials.php">正常的原材料管理</a><br>';
echo '<a href="debug_params.php">参数调试</a><br>';
?>
