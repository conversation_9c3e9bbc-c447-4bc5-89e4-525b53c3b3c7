<?php require_once __DIR__ . '/../../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar"></i> <?php echo $title; ?></h2>
    <div class="btn-group">
        <a href="index.php?controller=inventory&action=reports&type=overview" class="btn btn-primary">
            <i class="fas fa-chart-pie"></i> 库存概览
        </a>
        <a href="index.php?controller=inventory&action=reports&type=value" class="btn btn-outline-info">
            <i class="fas fa-dollar-sign"></i> 价值分析
        </a>
        <a href="index.php?controller=inventory&action=reports&type=turnover" class="btn btn-outline-success">
            <i class="fas fa-sync-alt"></i> 周转率
        </a>
        <a href="index.php?controller=inventory&action=index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
    </div>
</div>

<!-- 总体统计 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>¥<?php echo number_format($materialValue, 2); ?></h3>
                <p class="mb-0">原材料库存价值</p>
                <small><?php echo count($materials); ?> 种原材料</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>¥<?php echo number_format($productValue, 2); ?></h3>
                <p class="mb-0">成品库存价值</p>
                <small><?php echo count($products); ?> 种成品</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>¥<?php echo number_format($totalValue, 2); ?></h3>
                <p class="mb-0">总库存价值</p>
                <small>原材料 + 成品</small>
            </div>
        </div>
    </div>
</div>

<!-- 原材料库存详情 -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-cubes"></i> 原材料库存详情</h6>
    </div>
    <div class="card-body">
        <?php if (empty($materials)): ?>
            <p class="text-muted text-center">暂无原材料数据</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>原材料名称</th>
                            <th>规格</th>
                            <th>当前库存</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>库存价值</th>
                            <th>库存状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($materials as $material): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($material['name']); ?></strong>
                            </td>
                            <td>
                                <?php echo $material['specification'] ? htmlspecialchars($material['specification']) : '-'; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $material['stock_quantity'] < 10 ? 'warning' : 'success'; ?>">
                                    <?php echo number_format($material['stock_quantity'], 2); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($material['unit']); ?></td>
                            <td>¥<?php echo number_format($material['unit_price'], 2); ?></td>
                            <td>
                                <strong>¥<?php echo number_format($material['stock_value'], 2); ?></strong>
                            </td>
                            <td>
                                <?php if ($material['stock_quantity'] < 5): ?>
                                    <span class="badge bg-danger">严重不足</span>
                                <?php elseif ($material['stock_quantity'] < 10): ?>
                                    <span class="badge bg-warning">库存不足</span>
                                <?php elseif ($material['stock_quantity'] < 50): ?>
                                    <span class="badge bg-info">正常</span>
                                <?php else: ?>
                                    <span class="badge bg-success">充足</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <th colspan="5">原材料总计</th>
                            <th>¥<?php echo number_format($materialValue, 2); ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 成品库存详情 -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-box"></i> 成品库存详情</h6>
    </div>
    <div class="card-body">
        <?php if (empty($products)): ?>
            <p class="text-muted text-center">暂无成品数据</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>成品名称</th>
                            <th>规格</th>
                            <th>当前库存</th>
                            <th>单位</th>
                            <th>售价</th>
                            <th>库存价值</th>
                            <th>库存状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                            </td>
                            <td>
                                <?php echo $product['specification'] ? htmlspecialchars($product['specification']) : '-'; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $product['stock_quantity'] < 5 ? 'warning' : 'success'; ?>">
                                    <?php echo number_format($product['stock_quantity'], 3); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($product['unit']); ?></td>
                            <td>¥<?php echo number_format($product['selling_price'], 2); ?></td>
                            <td>
                                <strong>¥<?php echo number_format($product['stock_value'], 2); ?></strong>
                            </td>
                            <td>
                                <?php if ($product['stock_quantity'] < 2): ?>
                                    <span class="badge bg-danger">严重不足</span>
                                <?php elseif ($product['stock_quantity'] < 5): ?>
                                    <span class="badge bg-warning">库存不足</span>
                                <?php elseif ($product['stock_quantity'] < 20): ?>
                                    <span class="badge bg-info">正常</span>
                                <?php else: ?>
                                    <span class="badge bg-success">充足</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-info">
                            <th colspan="5">成品总计</th>
                            <th>¥<?php echo number_format($productValue, 2); ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 库存分析图表 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> 库存价值分布</h6>
            </div>
            <div class="card-body">
                <canvas id="valueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 库存状态分布</h6>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 库存分析建议
                </h6>
                <ul class="mb-0">
                    <li>定期检查低库存物品，及时补货</li>
                    <li>关注高价值物品的库存变化</li>
                    <li>优化库存结构，减少资金占用</li>
                    <li>建立合理的安全库存量</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-tools text-primary"></i> 快速操作
                </h6>
                <div class="d-grid gap-2">
                    <a href="index.php?controller=inventory&action=adjust" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> 库存调整
                    </a>
                    <a href="index.php?controller=inventory&action=logs" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-history"></i> 变动记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 库存价值分布饼图
const valueCtx = document.getElementById('valueChart').getContext('2d');
new Chart(valueCtx, {
    type: 'pie',
    data: {
        labels: ['原材料', '成品'],
        datasets: [{
            data: [<?php echo $materialValue; ?>, <?php echo $productValue; ?>],
            backgroundColor: ['#007bff', '#28a745'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 库存状态分布柱状图
const statusCtx = document.getElementById('statusChart').getContext('2d');
<?php
$materialStatus = ['充足' => 0, '正常' => 0, '不足' => 0, '严重不足' => 0];
foreach ($materials as $material) {
    if ($material['stock_quantity'] < 5) $materialStatus['严重不足']++;
    elseif ($material['stock_quantity'] < 10) $materialStatus['不足']++;
    elseif ($material['stock_quantity'] < 50) $materialStatus['正常']++;
    else $materialStatus['充足']++;
}

$productStatus = ['充足' => 0, '正常' => 0, '不足' => 0, '严重不足' => 0];
foreach ($products as $product) {
    if ($product['stock_quantity'] < 2) $productStatus['严重不足']++;
    elseif ($product['stock_quantity'] < 5) $productStatus['不足']++;
    elseif ($product['stock_quantity'] < 20) $productStatus['正常']++;
    else $productStatus['充足']++;
}
?>

new Chart(statusCtx, {
    type: 'bar',
    data: {
        labels: ['充足', '正常', '不足', '严重不足'],
        datasets: [{
            label: '原材料',
            data: [<?php echo implode(',', array_values($materialStatus)); ?>],
            backgroundColor: '#007bff'
        }, {
            label: '成品',
            data: [<?php echo implode(',', array_values($productStatus)); ?>],
            backgroundColor: '#28a745'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<?php require_once __DIR__ . '/../../layouts/footer.php'; ?>
