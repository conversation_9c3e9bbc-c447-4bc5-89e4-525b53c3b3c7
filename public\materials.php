<?php
/**
 * 独立的原材料管理系统
 * 完全绕过路由问题
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?controller=auth&action=login');
    exit;
}

require_once '../app/core/Database.php';
require_once '../app/models/Material.php';

$materialModel = new Material();
$message = '';
$messageType = '';

// 处理操作
$action = $_GET['action'] ?? 'list';

switch ($action) {
    case 'delete':
        if (isset($_GET['id'])) {
            try {
                $materialModel->delete($_GET['id']);
                $message = "原材料删除成功";
                $messageType = "success";
            } catch (Exception $e) {
                $message = "删除失败: " . $e->getMessage();
                $messageType = "danger";
            }
        }
        break;
}

// 获取数据
try {
    $search = $_GET['search'] ?? '';
    if (!empty($search)) {
        $materials = $materialModel->search($search);
    } else {
        $materials = $materialModel->getAll();
    }
    $stats = $materialModel->getStats();
} catch (Exception $e) {
    die("错误: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原材料管理 - 麻糍工厂销售系统</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="index.php">🧁 麻糍工厂</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">欢迎，<?php echo htmlspecialchars($_SESSION['name']); ?></span>
                <a class="nav-link" href="index.php">首页</a>
                <a class="nav-link" href="index.php?controller=auth&action=logout">退出</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-cubes text-primary"></i> 原材料管理</h2>
                <p class="text-muted mb-0">管理所有原材料信息和库存</p>
            </div>
            <div>
                <a href="material_add.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加原材料
                </a>
            </div>
        </div>

        <!-- 消息提示 -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_count']; ?></div>
                    <div class="stat-label">原材料总数</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-warning"><?php echo $stats['low_stock_count']; ?></div>
                    <div class="stat-label">库存不足</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-success">¥<?php echo number_format($stats['total_value'], 2); ?></div>
                    <div class="stat-label">总库存价值</div>
                </div>
            </div>
        </div>

        <!-- 搜索 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="搜索原材料名称或规格..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <?php if (!empty($search)): ?>
                            <a href="materials.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> 清除搜索
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- 原材料列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 原材料列表
                    <?php if (!empty($search)): ?>
                        <small class="text-muted">- 搜索结果: "<?php echo htmlspecialchars($search); ?>"</small>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($materials)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-cubes fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            <?php echo !empty($search) ? '未找到匹配的原材料' : '暂无原材料'; ?>
                        </h5>
                        <p class="text-muted">
                            <?php echo !empty($search) ? '请尝试其他搜索关键词' : '点击上方按钮添加第一个原材料'; ?>
                        </p>
                        <?php if (empty($search)): ?>
                            <a href="material_add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加原材料
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>规格</th>
                                    <th>单位</th>
                                    <th>库存数量</th>
                                    <th>单价</th>
                                    <th>库存价值</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($materials as $material): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($material['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($material['specification'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($material['unit']); ?></td>
                                        <td>
                                            <span class="<?php echo $material['stock_quantity'] < 5 ? 'text-warning fw-bold' : ''; ?>">
                                                <?php echo number_format($material['stock_quantity'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($material['unit_price']): ?>
                                                ¥<?php echo number_format($material['unit_price'], 2); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($material['unit_price']): ?>
                                                ¥<?php echo number_format($material['stock_quantity'] * $material['unit_price'], 2); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($material['stock_quantity'] < 5): ?>
                                                <span class="badge bg-warning">库存不足</span>
                                            <?php elseif ($material['stock_quantity'] == 0): ?>
                                                <span class="badge bg-danger">缺货</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">正常</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="material_edit.php?id=<?php echo $material['id']; ?>" 
                                                   class="btn btn-outline-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="materials.php?action=delete&id=<?php echo $material['id']; ?>" 
                                                   class="btn btn-outline-danger" 
                                                   onclick="return confirm('确定删除 <?php echo htmlspecialchars($material['name']); ?> 吗？')"
                                                   title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
</body>
</html>
