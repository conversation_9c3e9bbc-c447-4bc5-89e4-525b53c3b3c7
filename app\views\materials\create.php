<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-plus-circle text-primary"></i> 添加原材料</h2>
        <p class="text-muted mb-0">添加新的原材料到系统中</p>
    </div>
    <div>
        <a href="index.php?controller=material&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 添加表单 -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cube"></i> 原材料信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=material&action=store">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i> 原材料名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="例如：糯米粉" maxlength="100">
                                <div class="form-text">原材料的名称，必填项</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="specification" class="form-label">
                                    <i class="fas fa-info-circle"></i> 规格说明
                                </label>
                                <input type="text" class="form-control" id="specification" name="specification" 
                                       placeholder="例如：每袋500g" maxlength="100">
                                <div class="form-text">规格描述，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit" class="form-label">
                                    <i class="fas fa-balance-scale"></i> 单位 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="unit" name="unit" required>
                                    <option value="">请选择单位</option>
                                    <option value="袋">袋</option>
                                    <option value="包">包</option>
                                    <option value="盒">盒</option>
                                    <option value="瓶">瓶</option>
                                    <option value="罐">罐</option>
                                    <option value="kg">千克(kg)</option>
                                    <option value="g">克(g)</option>
                                    <option value="L">升(L)</option>
                                    <option value="ml">毫升(ml)</option>
                                    <option value="个">个</option>
                                </select>
                                <div class="form-text">计量单位，必填项</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label">
                                    <i class="fas fa-warehouse"></i> 初始库存
                                </label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       min="0" step="0.001" value="0" placeholder="0.000">
                                <div class="form-text">初始库存数量，默认为0</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit_price" class="form-label">
                                    <i class="fas fa-money-bill"></i> 单价
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                           min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="form-text">每单位的价格，用于成本计算，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 示例说明 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> 填写示例：</h6>
                        <ul class="mb-0">
                            <li><strong>糯米粉</strong> - 规格：每袋500g，单位：袋，单价：15.00元</li>
                            <li><strong>淀粉</strong> - 规格：每袋250g，单位：袋，单价：8.50元</li>
                            <li><strong>白糖</strong> - 规格：每袋1000g，单位：袋，单价：6.00元</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=material&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存原材料
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 自动计算库存价值
    $('#stock_quantity, #unit_price').on('input', function() {
        var quantity = parseFloat($('#stock_quantity').val()) || 0;
        var price = parseFloat($('#unit_price').val()) || 0;
        var value = quantity * price;
        
        if (value > 0) {
            $('#stock_value').text('库存价值：¥' + value.toFixed(2));
        } else {
            $('#stock_value').text('');
        }
    });
    
    // 单位选择后的提示
    $('#unit').on('change', function() {
        var unit = $(this).val();
        if (unit) {
            $('#stock_quantity').attr('placeholder', '请输入数量（' + unit + '）');
        }
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
