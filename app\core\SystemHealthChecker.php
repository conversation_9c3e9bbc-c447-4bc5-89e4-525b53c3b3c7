<?php
/**
 * 系统健康检查器
 * 自动检测系统各项功能是否正常
 */

require_once __DIR__ . '/Database.php';

class SystemHealthChecker
{
    private $db;
    private $results = [];
    private $startTime;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->startTime = microtime(true);
    }

    /**
     * 执行完整的系统检查
     */
    public function runFullCheck()
    {
        $this->results = [];
        
        // 基础环境检查
        $this->checkEnvironment();
        
        // 数据库检查
        $this->checkDatabase();
        
        // 文件系统检查
        $this->checkFileSystem();
        
        // 业务逻辑检查
        $this->checkBusinessLogic();
        
        // 性能检查
        $this->checkPerformance();
        
        return $this->generateReport();
    }

    /**
     * 检查PHP环境
     */
    private function checkEnvironment()
    {
        $checks = [];
        
        // PHP版本检查
        $phpVersion = PHP_VERSION;
        $checks['php_version'] = [
            'name' => 'PHP版本',
            'status' => version_compare($phpVersion, '7.0', '>=') ? 'pass' : 'fail',
            'message' => "当前版本: $phpVersion",
            'expected' => '>=7.0'
        ];
        
        // 必需扩展检查
        $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            $checks["ext_$ext"] = [
                'name' => "PHP扩展: $ext",
                'status' => extension_loaded($ext) ? 'pass' : 'fail',
                'message' => extension_loaded($ext) ? '已加载' : '未加载',
                'expected' => '已加载'
            ];
        }
        
        // 内存限制检查
        $memoryLimit = ini_get('memory_limit');
        $checks['memory_limit'] = [
            'name' => '内存限制',
            'status' => 'info',
            'message' => $memoryLimit,
            'expected' => '>=128M'
        ];
        
        $this->results['environment'] = $checks;
    }

    /**
     * 检查数据库连接和表结构
     */
    private function checkDatabase()
    {
        $checks = [];
        
        // 数据库连接检查
        try {
            $this->db->fetch("SELECT 1");
            $checks['connection'] = [
                'name' => '数据库连接',
                'status' => 'pass',
                'message' => '连接正常',
                'expected' => '可连接'
            ];
        } catch (Exception $e) {
            $checks['connection'] = [
                'name' => '数据库连接',
                'status' => 'fail',
                'message' => $e->getMessage(),
                'expected' => '可连接'
            ];
        }
        
        // 表结构检查
        $requiredTables = [
            'users', 'materials', 'products', 'customers', 
            'recipes', 'recipe_items', 'sales', 'sale_items', 'inventory_logs'
        ];
        
        foreach ($requiredTables as $table) {
            try {
                $result = $this->db->fetch("SELECT COUNT(*) as count FROM $table");
                $checks["table_$table"] = [
                    'name' => "数据表: $table",
                    'status' => 'pass',
                    'message' => "记录数: {$result['count']}",
                    'expected' => '表存在'
                ];
            } catch (Exception $e) {
                $checks["table_$table"] = [
                    'name' => "数据表: $table",
                    'status' => 'fail',
                    'message' => '表不存在或无法访问',
                    'expected' => '表存在'
                ];
            }
        }
        
        $this->results['database'] = $checks;
    }

    /**
     * 检查文件系统
     */
    private function checkFileSystem()
    {
        $checks = [];
        
        // 关键目录检查
        $requiredDirs = [
            'app/controllers' => '控制器目录',
            'app/models' => '模型目录',
            'app/views' => '视图目录',
            'app/core' => '核心目录',
            'public' => '公共目录'
        ];
        
        foreach ($requiredDirs as $dir => $name) {
            $fullPath = __DIR__ . '/../../' . $dir;
            $checks["dir_$dir"] = [
                'name' => $name,
                'status' => is_dir($fullPath) ? 'pass' : 'fail',
                'message' => is_dir($fullPath) ? '目录存在' : '目录不存在',
                'expected' => '目录存在'
            ];
        }
        
        // 关键文件检查
        $requiredFiles = [
            'app/core/Database.php' => '数据库核心文件',
            'app/core/Controller.php' => '控制器基类',
            'public/index.php' => '入口文件'
        ];
        
        foreach ($requiredFiles as $file => $name) {
            $fullPath = __DIR__ . '/../../' . $file;
            $checks["file_$file"] = [
                'name' => $name,
                'status' => file_exists($fullPath) ? 'pass' : 'fail',
                'message' => file_exists($fullPath) ? '文件存在' : '文件不存在',
                'expected' => '文件存在'
            ];
        }
        
        $this->results['filesystem'] = $checks;
    }

    /**
     * 检查业务逻辑
     */
    private function checkBusinessLogic()
    {
        $checks = [];
        
        // 测试基本CRUD操作
        try {
            // 测试材料查询
            $materials = $this->db->fetchAll("SELECT * FROM materials LIMIT 1");
            $checks['materials_query'] = [
                'name' => '原材料查询',
                'status' => 'pass',
                'message' => '查询正常',
                'expected' => '可查询'
            ];
        } catch (Exception $e) {
            $checks['materials_query'] = [
                'name' => '原材料查询',
                'status' => 'fail',
                'message' => $e->getMessage(),
                'expected' => '可查询'
            ];
        }
        
        // 测试数据一致性
        try {
            $inconsistentRecipes = $this->db->fetchAll("
                SELECT r.id FROM recipes r 
                LEFT JOIN products p ON r.product_id = p.id 
                WHERE p.id IS NULL
            ");
            
            $checks['data_consistency'] = [
                'name' => '数据一致性',
                'status' => empty($inconsistentRecipes) ? 'pass' : 'warning',
                'message' => empty($inconsistentRecipes) ? '数据一致' : '发现' . count($inconsistentRecipes) . '条不一致数据',
                'expected' => '数据一致'
            ];
        } catch (Exception $e) {
            $checks['data_consistency'] = [
                'name' => '数据一致性',
                'status' => 'fail',
                'message' => $e->getMessage(),
                'expected' => '数据一致'
            ];
        }
        
        $this->results['business'] = $checks;
    }

    /**
     * 检查系统性能
     */
    private function checkPerformance()
    {
        $checks = [];
        
        // 数据库查询性能
        $start = microtime(true);
        try {
            $this->db->fetchAll("SELECT * FROM materials LIMIT 100");
            $queryTime = (microtime(true) - $start) * 1000;
            
            $checks['query_performance'] = [
                'name' => '数据库查询性能',
                'status' => $queryTime < 100 ? 'pass' : ($queryTime < 500 ? 'warning' : 'fail'),
                'message' => sprintf('%.2fms', $queryTime),
                'expected' => '<100ms'
            ];
        } catch (Exception $e) {
            $checks['query_performance'] = [
                'name' => '数据库查询性能',
                'status' => 'fail',
                'message' => $e->getMessage(),
                'expected' => '<100ms'
            ];
        }
        
        // 内存使用检查
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        $checks['memory_usage'] = [
            'name' => '内存使用',
            'status' => $memoryUsage < 32 ? 'pass' : ($memoryUsage < 64 ? 'warning' : 'fail'),
            'message' => sprintf('%.2fMB', $memoryUsage),
            'expected' => '<32MB'
        ];
        
        $this->results['performance'] = $checks;
    }

    /**
     * 生成检查报告
     */
    private function generateReport()
    {
        $totalTime = (microtime(true) - $this->startTime) * 1000;
        
        $summary = [
            'total_checks' => 0,
            'passed' => 0,
            'failed' => 0,
            'warnings' => 0,
            'execution_time' => $totalTime
        ];
        
        foreach ($this->results as $category => $checks) {
            foreach ($checks as $check) {
                $summary['total_checks']++;
                switch ($check['status']) {
                    case 'pass':
                        $summary['passed']++;
                        break;
                    case 'fail':
                        $summary['failed']++;
                        break;
                    case 'warning':
                        $summary['warnings']++;
                        break;
                }
            }
        }
        
        return [
            'summary' => $summary,
            'details' => $this->results,
            'timestamp' => date('Y-m-d H:i:s'),
            'overall_status' => $summary['failed'] > 0 ? 'fail' : ($summary['warnings'] > 0 ? 'warning' : 'pass')
        ];
    }

    /**
     * 快速健康检查（仅检查关键项目）
     */
    public function quickCheck()
    {
        $checks = [];
        
        // 数据库连接
        try {
            $this->db->fetch("SELECT 1");
            $checks['database'] = 'pass';
        } catch (Exception $e) {
            $checks['database'] = 'fail';
        }
        
        // 关键表存在性
        try {
            $this->db->fetch("SELECT COUNT(*) FROM users");
            $this->db->fetch("SELECT COUNT(*) FROM materials");
            $checks['tables'] = 'pass';
        } catch (Exception $e) {
            $checks['tables'] = 'fail';
        }
        
        return $checks;
    }
}
