<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="login-container">
    <div class="login-card">
        <div class="text-center mb-4">
            <h2 class="login-title">🧁 麻糍工厂</h2>
            <p class="text-muted">销售管理系统</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success" role="alert">
                <?php echo $success; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="index.php?controller=auth&action=doLogin">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            
            <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input type="text" class="form-control" id="username" name="username" required 
                       placeholder="请输入用户名" autocomplete="username">
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input type="password" class="form-control" id="password" name="password" required 
                       placeholder="请输入密码" autocomplete="current-password">
            </div>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                <label class="form-check-label" for="remember">
                    记住我
                </label>
            </div>
            
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                忘记密码？请联系系统管理员
            </small>
        </div>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                &copy; <?php echo date('Y'); ?> 麻糍工厂销售系统
            </small>
        </div>
    </div>
</div>

<style>
/* 登录页面特殊样式 */
body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.login-container {
    position: relative;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0.05"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23a)"/></svg>');
    z-index: -1;
}

.login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* 响应式调整 */
@media (max-width: 576px) {
    .login-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
}
</style>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
