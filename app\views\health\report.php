<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-medical-alt"></i> <?php echo $title; ?></h2>
    <div class="btn-group">
        <a href="index.php?controller=health&action=index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
        <button type="button" class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> 打印报告
        </button>
        <button type="button" class="btn btn-success" onclick="exportReport()">
            <i class="fas fa-download"></i> 导出
        </button>
    </div>
</div>

<!-- 报告摘要 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-<?php echo $report['overall_status'] === 'pass' ? 'success' : ($report['overall_status'] === 'warning' ? 'warning' : 'danger'); ?> text-white">
            <div class="card-body text-center">
                <h3><?php echo $report['overall_status'] === 'pass' ? '健康' : ($report['overall_status'] === 'warning' ? '警告' : '异常'); ?></h3>
                <p class="mb-0">总体状态</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3><?php echo $report['summary']['passed']; ?></h3>
                <p class="mb-0">通过检查</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3><?php echo $report['summary']['warnings']; ?></h3>
                <p class="mb-0">警告项目</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h3><?php echo $report['summary']['failed']; ?></h3>
                <p class="mb-0">失败项目</p>
            </div>
        </div>
    </div>
</div>

<!-- 报告信息 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>检查时间:</strong><br>
                <?php echo $report['timestamp']; ?>
            </div>
            <div class="col-md-3">
                <strong>执行时间:</strong><br>
                <?php echo number_format($report['summary']['execution_time'], 2); ?>ms
            </div>
            <div class="col-md-3">
                <strong>检查项目:</strong><br>
                <?php echo $report['summary']['total_checks']; ?> 项
            </div>
            <div class="col-md-3">
                <strong>成功率:</strong><br>
                <?php echo number_format(($report['summary']['passed'] / $report['summary']['total_checks']) * 100, 1); ?>%
            </div>
        </div>
    </div>
</div>

<!-- 详细检查结果 -->
<?php foreach ($report['details'] as $category => $checks): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-<?php 
                echo match($category) {
                    'environment' => 'server',
                    'database' => 'database',
                    'filesystem' => 'folder',
                    'business' => 'cogs',
                    'performance' => 'tachometer-alt',
                    default => 'check'
                };
            ?>"></i>
            <?php 
                echo match($category) {
                    'environment' => 'PHP环境检查',
                    'database' => '数据库检查',
                    'filesystem' => '文件系统检查',
                    'business' => '业务逻辑检查',
                    'performance' => '性能检查',
                    default => $category
                };
            ?>
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>检查项目</th>
                        <th>状态</th>
                        <th>当前值</th>
                        <th>期望值</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($checks as $checkName => $check): ?>
                    <tr class="<?php 
                        echo match($check['status']) {
                            'pass' => 'table-success',
                            'warning' => 'table-warning',
                            'fail' => 'table-danger',
                            default => ''
                        };
                    ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($check['name']); ?></strong>
                        </td>
                        <td>
                            <span class="badge bg-<?php 
                                echo match($check['status']) {
                                    'pass' => 'success',
                                    'warning' => 'warning',
                                    'fail' => 'danger',
                                    default => 'secondary'
                                };
                            ?>">
                                <i class="fas fa-<?php 
                                    echo match($check['status']) {
                                        'pass' => 'check',
                                        'warning' => 'exclamation-triangle',
                                        'fail' => 'times',
                                        default => 'info'
                                    };
                                ?>"></i>
                                <?php 
                                    echo match($check['status']) {
                                        'pass' => '通过',
                                        'warning' => '警告',
                                        'fail' => '失败',
                                        'info' => '信息',
                                        default => $check['status']
                                    };
                                ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($check['message']); ?></td>
                        <td><?php echo htmlspecialchars($check['expected']); ?></td>
                        <td>
                            <?php if ($check['status'] === 'fail'): ?>
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-circle"></i>
                                    需要立即处理
                                </small>
                            <?php elseif ($check['status'] === 'warning'): ?>
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    建议关注
                                </small>
                            <?php else: ?>
                                <small class="text-success">
                                    <i class="fas fa-check"></i>
                                    运行正常
                                </small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- 建议和解决方案 -->
<?php if ($report['summary']['failed'] > 0 || $report['summary']['warnings'] > 0): ?>
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-lightbulb"></i> 建议和解决方案</h5>
    </div>
    <div class="card-body">
        <?php if ($report['summary']['failed'] > 0): ?>
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-circle"></i> 严重问题</h6>
            <ul class="mb-0">
                <?php foreach ($report['details'] as $category => $checks): ?>
                    <?php foreach ($checks as $check): ?>
                        <?php if ($check['status'] === 'fail'): ?>
                        <li>
                            <strong><?php echo htmlspecialchars($check['name']); ?>:</strong>
                            <?php echo htmlspecialchars($check['message']); ?>
                            <?php
                            // 提供解决建议
                            $suggestions = [
                                'database' => '检查数据库连接配置，确保数据库服务正在运行',
                                'table' => '运行数据库初始化脚本创建缺失的表',
                                'php_version' => '升级PHP版本到7.0或更高版本',
                                'ext_' => '安装缺失的PHP扩展',
                                'dir_' => '创建缺失的目录并设置正确的权限',
                                'file_' => '检查文件是否存在，确保路径正确'
                            ];
                            
                            foreach ($suggestions as $key => $suggestion) {
                                if (strpos($check['name'], $key) !== false || strpos($checkName, $key) !== false) {
                                    echo " <small class=\"text-muted\">建议: $suggestion</small>";
                                    break;
                                }
                            }
                            ?>
                        </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if ($report['summary']['warnings'] > 0): ?>
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> 需要关注的问题</h6>
            <ul class="mb-0">
                <?php foreach ($report['details'] as $category => $checks): ?>
                    <?php foreach ($checks as $check): ?>
                        <?php if ($check['status'] === 'warning'): ?>
                        <li>
                            <strong><?php echo htmlspecialchars($check['name']); ?>:</strong>
                            <?php echo htmlspecialchars($check['message']); ?>
                        </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<!-- 系统信息 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle"></i> 系统信息</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>PHP版本:</strong></td>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <td><strong>服务器软件:</strong></td>
                        <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>操作系统:</strong></td>
                        <td><?php echo PHP_OS; ?></td>
                    </tr>
                    <tr>
                        <td><strong>内存限制:</strong></td>
                        <td><?php echo ini_get('memory_limit'); ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>当前内存使用:</strong></td>
                        <td><?php echo number_format(memory_get_usage(true) / 1024 / 1024, 2); ?>MB</td>
                    </tr>
                    <tr>
                        <td><strong>峰值内存使用:</strong></td>
                        <td><?php echo number_format(memory_get_peak_usage(true) / 1024 / 1024, 2); ?>MB</td>
                    </tr>
                    <tr>
                        <td><strong>执行时间限制:</strong></td>
                        <td><?php echo ini_get('max_execution_time'); ?>秒</td>
                    </tr>
                    <tr>
                        <td><strong>文件上传限制:</strong></td>
                        <td><?php echo ini_get('upload_max_filesize'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // 创建导出数据
    const reportData = <?php echo json_encode($report); ?>;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(reportData, null, 2));
    
    // 创建下载链接
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "health_report_" + new Date().toISOString().slice(0,10) + ".json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
}

// 打印样式
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
    
    .table {
        font-size: 12px;
    }
}
</style>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
