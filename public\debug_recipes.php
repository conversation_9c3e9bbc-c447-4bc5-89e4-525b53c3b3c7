<?php
/**
 * 调试配方数据
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

require_once '../app/core/Database.php';

$db = Database::getInstance();

echo "<h2>配方数据调试</h2>";

echo "<h3>1. 配方表 (recipes)</h3>";
$recipes = $db->fetchAll("SELECT * FROM recipes ORDER BY id");
if (empty($recipes)) {
    echo "<p>配方表为空</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Product ID</th><th>Name</th><th>Created At</th><th>Updated At</th></tr>";
    foreach ($recipes as $recipe) {
        echo "<tr>";
        echo "<td>" . $recipe['id'] . "</td>";
        echo "<td>" . $recipe['product_id'] . "</td>";
        echo "<td>" . ($recipe['name'] ?: '默认配方') . "</td>";
        echo "<td>" . $recipe['created_at'] . "</td>";
        echo "<td>" . $recipe['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>2. 配方项目表 (recipe_items)</h3>";
$recipeItems = $db->fetchAll("SELECT * FROM recipe_items ORDER BY recipe_id, id");
if (empty($recipeItems)) {
    echo "<p>配方项目表为空</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Recipe ID</th><th>Material ID</th><th>Quantity</th><th>Unit</th></tr>";
    foreach ($recipeItems as $item) {
        echo "<tr>";
        echo "<td>" . $item['id'] . "</td>";
        echo "<td>" . $item['recipe_id'] . "</td>";
        echo "<td>" . $item['material_id'] . "</td>";
        echo "<td>" . $item['quantity_needed'] . "</td>";
        echo "<td>" . $item['unit_of_material'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>3. 成品表 (products)</h3>";
$products = $db->fetchAll("SELECT * FROM products ORDER BY id");
if (empty($products)) {
    echo "<p>成品表为空</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Unit</th><th>Stock</th><th>Price</th></tr>";
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['name'] . "</td>";
        echo "<td>" . $product['unit'] . "</td>";
        echo "<td>" . $product['stock_quantity'] . "</td>";
        echo "<td>" . $product['selling_price'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>4. 原材料表 (materials)</h3>";
$materials = $db->fetchAll("SELECT * FROM materials ORDER BY id");
if (empty($materials)) {
    echo "<p>原材料表为空</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Unit</th><th>Stock</th><th>Price</th></tr>";
    foreach ($materials as $material) {
        echo "<tr>";
        echo "<td>" . $material['id'] . "</td>";
        echo "<td>" . $material['name'] . "</td>";
        echo "<td>" . $material['unit'] . "</td>";
        echo "<td>" . $material['stock_quantity'] . "</td>";
        echo "<td>" . $material['unit_price'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>5. 配方统计查询</h3>";
$stats = $db->fetchAll("
    SELECT r.*, p.name as product_name, p.unit as product_unit,
           (SELECT COUNT(*) FROM recipe_items ri WHERE ri.recipe_id = r.id) as material_count
    FROM recipes r
    JOIN products p ON r.product_id = p.id
    ORDER BY p.name
");

if (empty($stats)) {
    echo "<p>没有配方数据</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Recipe ID</th><th>Product Name</th><th>Recipe Name</th><th>Material Count</th></tr>";
    foreach ($stats as $stat) {
        echo "<tr>";
        echo "<td>" . $stat['id'] . "</td>";
        echo "<td>" . $stat['product_name'] . "</td>";
        echo "<td>" . ($stat['name'] ?: '默认配方') . "</td>";
        echo "<td>" . $stat['material_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>6. 配方统计信息</h3>";
$totalRecipes = $db->fetch("SELECT COUNT(*) as count FROM recipes");
$productsWithRecipe = $db->fetch("SELECT COUNT(DISTINCT product_id) as count FROM recipes");
$productsWithoutRecipe = $db->fetch("
    SELECT COUNT(*) as count FROM products p 
    WHERE NOT EXISTS (SELECT 1 FROM recipes r WHERE r.product_id = p.id)
");

echo "<p>总配方数: " . $totalRecipes['count'] . "</p>";
echo "<p>有配方的成品数: " . $productsWithRecipe['count'] . "</p>";
echo "<p>无配方的成品数: " . $productsWithoutRecipe['count'] . "</p>";

echo "<br><a href='index.php?controller=recipe&action=index'>返回配方管理</a>";
?>
