<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题和操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-users text-primary"></i> 客户管理</h2>
        <p class="text-muted mb-0">管理客户信息和账目往来</p>
    </div>
    <div>
        <a href="index.php?controller=customer&action=create" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加客户
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_count']; ?></div>
            <div class="stat-label">客户总数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-warning"><?php echo $stats['debt_customers_count']; ?></div>
            <div class="stat-label">有欠款客户</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-danger">¥<?php echo number_format($stats['total_debt'], 2); ?></div>
            <div class="stat-label">总欠款金额</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-info">¥<?php echo number_format($stats['average_debt'], 2); ?></div>
            <div class="stat-label">平均欠款</div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="controller" value="customer">
            <input type="hidden" name="action" value="index">
            
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="搜索客户名称或联系方式..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-control" name="filter" onchange="this.form.submit()">
                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>所有客户</option>
                    <option value="debt" <?php echo $filter === 'debt' ? 'selected' : ''; ?>>有欠款</option>
                    <option value="no_debt" <?php echo $filter === 'no_debt' ? 'selected' : ''; ?>>无欠款</option>
                </select>
            </div>
            <div class="col-md-3">
                <?php if (!empty($search) || $filter !== 'all'): ?>
                    <a href="index.php?controller=customer&action=index" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 清除筛选
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- 客户列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> 客户列表
            <?php if (!empty($search)): ?>
                <small class="text-muted">- 搜索结果: "<?php echo htmlspecialchars($search); ?>"</small>
            <?php endif; ?>
            <?php if ($filter !== 'all'): ?>
                <small class="text-muted">- 筛选: <?php echo $filter === 'debt' ? '有欠款' : '无欠款'; ?></small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($customers)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    <?php echo !empty($search) || $filter !== 'all' ? '未找到匹配的客户' : '暂无客户'; ?>
                </h5>
                <p class="text-muted">
                    <?php echo !empty($search) || $filter !== 'all' ? '请尝试其他搜索条件' : '点击上方按钮添加第一个客户'; ?>
                </p>
                <?php if (empty($search) && $filter === 'all'): ?>
                    <a href="index.php?controller=customer&action=create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加客户
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>客户名称</th>
                            <th>联系方式</th>
                            <th>当前余额</th>
                            <th>账目状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($customers as $customer): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                </td>
                                <td>
                                    <?php if (!empty($customer['contact_info'])): ?>
                                        <?php echo htmlspecialchars($customer['contact_info']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">未填写</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="<?php echo $customer['balance'] > 0 ? 'text-danger fw-bold' : ($customer['balance'] < 0 ? 'text-success' : 'text-muted'); ?>">
                                        ¥<?php echo number_format($customer['balance'], 2); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($customer['balance'] > 0): ?>
                                        <span class="badge bg-warning">欠款</span>
                                    <?php elseif ($customer['balance'] < 0): ?>
                                        <span class="badge bg-success">预付</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">结清</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($customer['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="index.php?controller=customer&action=detail&id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?controller=customer&action=edit&id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?controller=sale&action=create&customer_id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-outline-success" title="快速开单">
                                            <i class="fas fa-shopping-cart"></i>
                                        </a>
                                        <a href="index.php?controller=customer&action=delete&id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-outline-danger btn-delete" 
                                           data-item-name="<?php echo htmlspecialchars($customer['name']); ?>"
                                           title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 快速操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 客户管理提示
                </h6>
                <ul class="mb-0">
                    <li>余额为正数表示客户欠款</li>
                    <li>余额为负数表示客户预付款</li>
                    <li>可以通过客户详情页面调整余额</li>
                    <li>删除客户前请确保无未结清账目</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-pie text-info"></i> 账目统计
                </h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="text-success">
                            <strong><?php echo $stats['total_count'] - $stats['debt_customers_count']; ?></strong>
                            <small class="d-block">正常客户</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-warning">
                            <strong><?php echo $stats['debt_customers_count']; ?></strong>
                            <small class="d-block">欠款客户</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-danger">
                            <strong>¥<?php echo number_format($stats['total_debt'], 0); ?></strong>
                            <small class="d-block">总欠款</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
