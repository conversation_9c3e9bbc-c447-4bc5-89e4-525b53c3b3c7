<?php
/**
 * 清理调试数据
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '权限不足']);
    exit;
}

header('Content-Type: application/json');

try {
    $debugDir = __DIR__ . '/..';
    $cleaned = [];
    
    // 清理临时文件
    $tempFiles = glob($debugDir . '/temp/*');
    foreach ($tempFiles as $file) {
        if (is_file($file)) {
            unlink($file);
            $cleaned[] = basename($file);
        }
    }
    
    // 清理日志文件
    $logFiles = glob($debugDir . '/logs/*');
    foreach ($logFiles as $file) {
        if (is_file($file)) {
            unlink($file);
            $cleaned[] = basename($file);
        }
    }
    
    // 清理缓存文件
    $cacheFiles = glob($debugDir . '/cache/*');
    foreach ($cacheFiles as $file) {
        if (is_file($file)) {
            unlink($file);
            $cleaned[] = basename($file);
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => '调试数据清理完成',
        'cleaned_files' => $cleaned,
        'count' => count($cleaned)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '清理失败: ' . $e->getMessage()
    ]);
}
?>
