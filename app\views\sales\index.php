<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题和操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-shopping-cart text-primary"></i> 销售管理</h2>
        <p class="text-muted mb-0">管理销售记录和开单业务</p>
    </div>
    <div>
        <a href="index.php?controller=sale&action=create" class="btn btn-primary">
            <i class="fas fa-plus"></i> 快速开单
        </a>
    </div>
</div>

<!-- 今日统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-primary"><?php echo $todayStats['total_sales']; ?></div>
            <div class="stat-label">今日销售单数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-success">¥<?php echo number_format($todayStats['total_amount'], 2); ?></div>
            <div class="stat-label">今日销售额</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-info">¥<?php echo number_format($todayStats['total_payment'], 2); ?></div>
            <div class="stat-label">今日收款</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-warning">¥<?php echo number_format($todayStats['average_amount'], 2); ?></div>
            <div class="stat-label">平均客单价</div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="controller" value="sale">
            <input type="hidden" name="action" value="index">
            
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="搜索客户名称或备注..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" name="start_date" 
                       placeholder="开始日期" value="<?php echo htmlspecialchars($startDate); ?>">
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" name="end_date" 
                       placeholder="结束日期" value="<?php echo htmlspecialchars($endDate); ?>">
            </div>
            <div class="col-md-2">
                <?php if (!empty($search) || !empty($startDate) || !empty($endDate)): ?>
                    <a href="index.php?controller=sale&action=index" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times"></i> 清除
                    </a>
                <?php else: ?>
                    <button type="submit" class="btn btn-primary w-100">筛选</button>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- 期间统计 -->
<?php if (!empty($startDate) || !empty($endDate)): ?>
<div class="alert alert-info">
    <div class="row text-center">
        <div class="col-md-3">
            <strong><?php echo $stats['total_sales']; ?></strong>
            <small class="d-block">销售单数</small>
        </div>
        <div class="col-md-3">
            <strong>¥<?php echo number_format($stats['total_amount'], 2); ?></strong>
            <small class="d-block">销售总额</small>
        </div>
        <div class="col-md-3">
            <strong>¥<?php echo number_format($stats['total_payment'], 2); ?></strong>
            <small class="d-block">收款总额</small>
        </div>
        <div class="col-md-3">
            <strong>¥<?php echo number_format($stats['average_amount'], 2); ?></strong>
            <small class="d-block">平均客单价</small>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- 销售记录列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> 销售记录
            <?php if (!empty($search)): ?>
                <small class="text-muted">- 搜索结果: "<?php echo htmlspecialchars($search); ?>"</small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($sales)): ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    <?php echo !empty($search) || !empty($startDate) || !empty($endDate) ? '未找到匹配的销售记录' : '暂无销售记录'; ?>
                </h5>
                <p class="text-muted">
                    <?php echo !empty($search) || !empty($startDate) || !empty($endDate) ? '请尝试其他搜索条件' : '点击上方按钮创建第一个销售单'; ?>
                </p>
                <?php if (empty($search) && empty($startDate) && empty($endDate)): ?>
                    <a href="index.php?controller=sale&action=create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 快速开单
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>销售单号</th>
                            <th>客户</th>
                            <th>销售金额</th>
                            <th>收款金额</th>
                            <th>欠款</th>
                            <th>销售时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sales as $sale): ?>
                            <?php $debt = $sale['total_amount'] - $sale['payment_amount']; ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?></strong>
                                </td>
                                <td>
                                    <?php if ($sale['customer_name']): ?>
                                        <?php echo htmlspecialchars($sale['customer_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">散客</span>
                                    <?php endif; ?>
                                </td>
                                <td>¥<?php echo number_format($sale['total_amount'], 2); ?></td>
                                <td>¥<?php echo number_format($sale['payment_amount'], 2); ?></td>
                                <td>
                                    <span class="<?php echo $debt > 0 ? 'text-danger fw-bold' : 'text-success'; ?>">
                                        ¥<?php echo number_format($debt, 2); ?>
                                    </span>
                                </td>
                                <td><?php echo date('m-d H:i', strtotime($sale['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="index.php?controller=sale&action=detail&id=<?php echo $sale['id']; ?>"
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?controller=sale&action=print&id=<?php echo $sale['id']; ?>"
                                           class="btn btn-outline-secondary" title="打印" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="index.php?controller=sale&action=delete&id=<?php echo $sale['id']; ?>"
                                           class="btn btn-outline-danger btn-delete"
                                           data-item-name="销售单 #<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?>"
                                           title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 快速操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 销售管理提示
                </h6>
                <ul class="mb-0">
                    <li>删除销售单会自动恢复库存和客户余额</li>
                    <li>可以按客户名称或备注搜索销售记录</li>
                    <li>支持按日期范围筛选销售记录</li>
                    <li>红色金额表示客户仍有欠款</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-line text-success"></i> 快速操作
                </h6>
                <div class="d-grid gap-2">
                    <a href="index.php?controller=sale&action=create" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus"></i> 快速开单
                    </a>
                    <a href="index.php?controller=customer&action=index" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-users"></i> 客户管理
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
