<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题和操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-box text-primary"></i> 成品管理</h2>
        <p class="text-muted mb-0">管理所有成品信息和库存</p>
    </div>
    <div>
        <a href="index.php?controller=product&action=create" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加成品
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_count']; ?></div>
            <div class="stat-label">成品总数</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-warning"><?php echo $stats['low_stock_count']; ?></div>
            <div class="stat-label">库存不足</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-success">¥<?php echo number_format($stats['total_value'], 2); ?></div>
            <div class="stat-label">总库存价值</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number text-info"><?php echo $stats['with_recipe_count']; ?></div>
            <div class="stat-label">已配置配方</div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="controller" value="product">
            <input type="hidden" name="action" value="index">
            
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="搜索成品名称或规格..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <?php if (!empty($search)): ?>
                    <a href="index.php?controller=product&action=index" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 清除搜索
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- 成品列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> 成品列表
            <?php if (!empty($search)): ?>
                <small class="text-muted">- 搜索结果: "<?php echo htmlspecialchars($search); ?>"</small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    <?php echo !empty($search) ? '未找到匹配的成品' : '暂无成品'; ?>
                </h5>
                <p class="text-muted">
                    <?php echo !empty($search) ? '请尝试其他搜索关键词' : '点击上方按钮添加第一个成品'; ?>
                </p>
                <?php if (empty($search)): ?>
                    <a href="index.php?controller=product&action=create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加成品
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>规格</th>
                            <th>单位</th>
                            <th>库存数量</th>
                            <th>销售价格</th>
                            <th>库存价值</th>
                            <th>配方</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($product['specification'] ?? '-'); ?></td>
                                <td><?php echo htmlspecialchars($product['unit']); ?></td>
                                <td>
                                    <span class="<?php echo $product['stock_quantity'] < 10 ? 'text-warning fw-bold' : ''; ?>">
                                        <?php echo number_format($product['stock_quantity'], 2); ?>
                                    </span>
                                </td>
                                <td>¥<?php echo number_format($product['selling_price'], 2); ?></td>
                                <td>¥<?php echo number_format($product['stock_quantity'] * $product['selling_price'], 2); ?></td>
                                <td>
                                    <a href="index.php?controller=recipe&action=index&product_id=<?php echo $product['id']; ?>" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-list-ul"></i> 配方
                                    </a>
                                </td>
                                <td>
                                    <?php if ($product['stock_quantity'] <= 0): ?>
                                        <span class="badge bg-danger">缺货</span>
                                    <?php elseif ($product['stock_quantity'] < 10): ?>
                                        <span class="badge bg-warning">库存不足</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">正常</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="index.php?controller=product&action=detail&id=<?php echo $product['id']; ?>"
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?controller=product&action=edit&id=<?php echo $product['id']; ?>" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?controller=product&action=delete&id=<?php echo $product['id']; ?>" 
                                           class="btn btn-outline-danger btn-delete" 
                                           data-item-name="<?php echo htmlspecialchars($product['name']); ?>"
                                           title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
