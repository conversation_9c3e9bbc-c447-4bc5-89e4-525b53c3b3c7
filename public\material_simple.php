<?php
/**
 * 简化版原材料管理页面
 * 用于调试和测试
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?controller=auth&action=login');
    exit;
}

// 引入必要文件
require_once '../app/core/Database.php';
require_once '../app/models/Material.php';

try {
    $materialModel = new Material();
    $materials = $materialModel->getAll();
    $stats = $materialModel->getStats();
} catch (Exception $e) {
    die("错误: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原材料管理 - 简化版</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .stat-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            text-align: center; 
            margin-bottom: 20px;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧁 原材料管理 (简化版)</h1>
        
        <div class="alert alert-info">
            <strong>当前用户:</strong> <?php echo htmlspecialchars($_SESSION['name'] ?? $_SESSION['username']); ?>
            | <a href="index.php">返回首页</a>
            | <a href="index.php?controller=auth&action=logout">退出登录</a>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_count']; ?></div>
                    <div>原材料总数</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-warning"><?php echo $stats['low_stock_count']; ?></div>
                    <div>库存不足</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-success">¥<?php echo number_format($stats['total_value'], 2); ?></div>
                    <div>总库存价值</div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-3">
            <a href="index.php?controller=material&action=create" class="btn btn-primary">添加原材料</a>
            <a href="index.php?controller=material&action=index" class="btn btn-secondary">完整版页面</a>
        </div>

        <!-- 原材料列表 -->
        <div class="card">
            <div class="card-header">
                <h5>原材料列表</h5>
            </div>
            <div class="card-body">
                <?php if (empty($materials)): ?>
                    <div class="text-center py-4">
                        <p class="text-muted">暂无原材料</p>
                        <a href="index.php?controller=material&action=create" class="btn btn-primary">添加第一个原材料</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>规格</th>
                                    <th>单位</th>
                                    <th>库存数量</th>
                                    <th>单价</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($materials as $material): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($material['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($material['specification'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($material['unit']); ?></td>
                                        <td>
                                            <span class="<?php echo $material['stock_quantity'] < 5 ? 'text-warning fw-bold' : ''; ?>">
                                                <?php echo number_format($material['stock_quantity'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($material['unit_price']): ?>
                                                ¥<?php echo number_format($material['unit_price'], 2); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($material['stock_quantity'] < 5): ?>
                                                <span class="badge bg-warning">库存不足</span>
                                            <?php elseif ($material['stock_quantity'] == 0): ?>
                                                <span class="badge bg-danger">缺货</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">正常</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="index.php?controller=material&action=edit&id=<?php echo $material['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">编辑</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="mt-4">
            <small class="text-muted">
                这是简化版页面，用于调试。如果此页面正常工作，说明基础功能没问题。
            </small>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
</body>
</html>
