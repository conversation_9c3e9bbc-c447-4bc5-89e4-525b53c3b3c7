/**
 * 麻糍工厂销售系统 - 自定义JavaScript
 */

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 确认删除对话框
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var itemName = $(this).data('item-name') || '此项目';
        
        if (confirm('确定要删除 "' + itemName + '" 吗？此操作不可撤销。')) {
            window.location.href = url;
        }
    });
    
    // 表单验证
    $('form').on('submit', function(e) {
        var form = this;
        var isValid = true;
        
        // 检查必填字段
        $(form).find('[required]').each(function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            if (!value) {
                isValid = false;
                $field.addClass('is-invalid');
                showFieldError($field, '此字段为必填项');
            } else {
                $field.removeClass('is-invalid');
                hideFieldError($field);
            }
        });
        
        // 检查数字字段
        $(form).find('input[type="number"]').each(function() {
            var $field = $(this);
            var value = parseFloat($field.val());
            var min = parseFloat($field.attr('min'));
            var max = parseFloat($field.attr('max'));
            
            if (!isNaN(min) && value < min) {
                isValid = false;
                $field.addClass('is-invalid');
                showFieldError($field, '值不能小于 ' + min);
            } else if (!isNaN(max) && value > max) {
                isValid = false;
                $field.addClass('is-invalid');
                showFieldError($field, '值不能大于 ' + max);
            } else {
                $field.removeClass('is-invalid');
                hideFieldError($field);
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showAlert('danger', '请检查表单中的错误信息');
        }
    });
    
    // 数字输入框自动格式化
    $('input[type="number"]').on('blur', function() {
        var $this = $(this);
        var value = parseFloat($this.val());
        var decimals = $this.data('decimals') || 2;
        
        if (!isNaN(value)) {
            $this.val(value.toFixed(decimals));
        }
    });
    
    // 自动保存草稿（可选功能）
    var autoSaveTimer;
    $('form[data-auto-save]').on('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            // 这里可以实现自动保存功能
            console.log('自动保存草稿...');
        }, 5000);
    });
});

/**
 * 显示警告消息
 */
function showAlert(type, message, duration = 5000) {
    var alertClass = 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 移除现有的警告
    $('.alert').remove();
    
    // 添加新警告到页面顶部
    $('main').prepend(alertHtml);
    
    // 自动隐藏
    if (duration > 0) {
        setTimeout(function() {
            $('.alert').fadeOut();
        }, duration);
    }
    
    // 滚动到顶部
    $('html, body').animate({ scrollTop: 0 }, 300);
}

/**
 * 显示字段错误
 */
function showFieldError($field, message) {
    hideFieldError($field);
    
    var errorHtml = `<div class="invalid-feedback">${message}</div>`;
    $field.after(errorHtml);
}

/**
 * 隐藏字段错误
 */
function hideFieldError($field) {
    $field.next('.invalid-feedback').remove();
}

/**
 * 加载指示器
 */
function showLoading($button) {
    var originalText = $button.text();
    $button.data('original-text', originalText);
    $button.html('<span class="loading"></span> 处理中...');
    $button.prop('disabled', true);
}

function hideLoading($button) {
    var originalText = $button.data('original-text');
    $button.text(originalText);
    $button.prop('disabled', false);
}

/**
 * 格式化数字显示
 */
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

/**
 * 格式化货币显示
 */
function formatCurrency(amount, symbol = '¥') {
    return symbol + formatNumber(amount, 2);
}

/**
 * AJAX请求封装
 */
function ajaxRequest(url, data, method = 'POST') {
    return $.ajax({
        url: url,
        method: method,
        data: data,
        dataType: 'json',
        beforeSend: function() {
            // 显示加载状态
        },
        complete: function() {
            // 隐藏加载状态
        },
        error: function(xhr, status, error) {
            showAlert('danger', '请求失败: ' + error);
        }
    });
}

/**
 * 快速录单相关功能
 */
var QuickEntry = {
    // 添加商品行
    addProductRow: function() {
        var template = $('#product-row-template').html();
        var newRow = $(template);
        $('#product-list').append(newRow);
        this.updateRowNumbers();
    },
    
    // 删除商品行
    removeProductRow: function($row) {
        $row.remove();
        this.updateRowNumbers();
        this.calculateTotal();
    },
    
    // 更新行号
    updateRowNumbers: function() {
        $('#product-list .product-row').each(function(index) {
            $(this).find('.row-number').text(index + 1);
        });
    },
    
    // 计算总金额
    calculateTotal: function() {
        var total = 0;
        $('#product-list .product-row').each(function() {
            var quantity = parseFloat($(this).find('.quantity').val()) || 0;
            var price = parseFloat($(this).find('.price').val()) || 0;
            var subtotal = quantity * price;
            $(this).find('.subtotal').text(formatCurrency(subtotal));
            total += subtotal;
        });
        $('#total-amount').text(formatCurrency(total));
    }
};

// 绑定快速录单事件
$(document).on('click', '.add-product-row', function() {
    QuickEntry.addProductRow();
});

$(document).on('click', '.remove-product-row', function() {
    QuickEntry.removeProductRow($(this).closest('.product-row'));
});

$(document).on('input', '.quantity, .price', function() {
    QuickEntry.calculateTotal();
});
