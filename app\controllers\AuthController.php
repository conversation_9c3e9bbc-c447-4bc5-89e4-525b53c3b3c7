<?php
/**
 * 认证控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/User.php';

class AuthController extends Controller
{
    private $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }

    /**
     * 显示登录页面
     */
    public function login()
    {
        // 如果已经登录，重定向到首页
        if ($this->isLoggedIn()) {
            $this->redirect('index.php?controller=home&action=index');
        }

        $error = $this->getFlash('error');
        $success = $this->getFlash('success');
        
        $this->view('auth/login', [
            'error' => $error,
            'success' => $success,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理登录请求
     */
    public function doLogin()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=auth&action=login');
        }

        // 验证CSRF Token
        $this->validateCsrfToken();

        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';

        // 验证输入
        $errors = $this->validate($_POST, [
            'username' => [
                'required' => true,
                'message' => '请输入用户名'
            ],
            'password' => [
                'required' => true,
                'message' => '请输入密码'
            ]
        ]);

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=auth&action=login');
        }

        // 查找用户
        $user = $this->userModel->findByUsername($username);
        
        if (!$user || !$this->userModel->verifyPassword($password, $user['password_hash'])) {
            $this->setFlash('error', '用户名或密码错误');
            $this->redirect('index.php?controller=auth&action=login');
        }

        // 登录成功，设置会话
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['name'] = $user['name'];

        $this->setFlash('success', '登录成功，欢迎回来！');
        $this->redirect('index.php?controller=home&action=index');
    }

    /**
     * 登出
     */
    public function logout()
    {
        // 清除会话数据
        session_unset();
        session_destroy();

        // 重新启动会话以设置flash消息
        session_start();
        $this->setFlash('success', '您已成功登出');
        
        $this->redirect('index.php?controller=auth&action=login');
    }
}
