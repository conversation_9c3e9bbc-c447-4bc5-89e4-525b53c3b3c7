<?php
/**
 * 销售控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Sale.php';
require_once __DIR__ . '/../models/Customer.php';
require_once __DIR__ . '/../models/Product.php';

class SaleController extends Controller
{
    private $saleModel;
    private $customerModel;
    private $productModel;

    public function __construct()
    {
        parent::__construct();
        $this->saleModel = new Sale();
        $this->customerModel = new Customer();
        $this->productModel = new Product();
    }

    /**
     * 销售记录列表页
     */
    public function index()
    {
        $this->requireAuth();
        
        $search = $_GET['search'] ?? '';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';
        
        if (!empty($search) || !empty($startDate) || !empty($endDate)) {
            $sales = $this->saleModel->search($search, $startDate, $endDate);
        } else {
            $sales = $this->saleModel->getAll('created_at', 'DESC', 50); // 最近50条
        }
        
        $stats = $this->saleModel->getStats($startDate, $endDate);
        $todayStats = $this->saleModel->getTodayStats();
        
        $this->view('sales/index', [
            'title' => '销售管理',
            'sales' => $sales,
            'stats' => $stats,
            'todayStats' => $todayStats,
            'search' => $search,
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
    }

    /**
     * 显示新增销售页面
     */
    public function create()
    {
        $this->requireAuth();
        
        $customerId = $_GET['customer_id'] ?? '';
        
        // 获取所有客户
        $customers = $this->customerModel->getAll();
        
        // 获取有库存的成品
        $products = $this->productModel->getAvailableForSale();
        
        $this->view('sales/create', [
            'title' => '快速开单',
            'customers' => $customers,
            'products' => $products,
            'selectedCustomerId' => $customerId,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理新增销售请求
     */
    public function store()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=sale&action=index');
        }

        $this->validateCsrfToken();

        $customerId = $_POST['customer_id'] ?? '';
        $paymentAmount = floatval($_POST['payment_amount'] ?? 0);
        $notes = trim($_POST['notes'] ?? '');
        $items = $_POST['items'] ?? [];

        // 验证输入
        if (empty($customerId)) {
            $this->setFlash('error', '请选择客户');
            $this->redirect('index.php?controller=sale&action=create');
        }

        if (empty($items)) {
            $this->setFlash('error', '请至少添加一个商品');
            $this->redirect('index.php?controller=sale&action=create');
        }

        // 验证和处理销售项目
        $saleItems = [];
        foreach ($items as $item) {
            if (empty($item['product_id']) || empty($item['quantity']) || empty($item['unit_price'])) {
                $this->setFlash('error', '请完整填写所有商品信息');
                $this->redirect('index.php?controller=sale&action=create');
            }

            $quantity = floatval($item['quantity']);
            $unitPrice = floatval($item['unit_price']);

            if ($quantity <= 0 || $unitPrice <= 0) {
                $this->setFlash('error', '商品数量和单价必须大于0');
                $this->redirect('index.php?controller=sale&action=create');
            }

            // 检查库存
            $product = $this->productModel->getById($item['product_id']);
            if (!$product) {
                $this->setFlash('error', '商品不存在');
                $this->redirect('index.php?controller=sale&action=create');
            }

            if ($product['stock_quantity'] < $quantity) {
                $this->setFlash('error', "商品 {$product['name']} 库存不足，当前库存：{$product['stock_quantity']}");
                $this->redirect('index.php?controller=sale&action=create');
            }

            $saleItems[] = [
                'product_id' => $item['product_id'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice
            ];
        }

        try {
            $saleId = $this->saleModel->create($customerId, $saleItems, $paymentAmount, $notes);
            
            $this->setFlash('success', '销售单创建成功');
            $this->redirect('index.php?controller=sale&action=detail&id=' . $saleId);
            
        } catch (Exception $e) {
            $this->setFlash('error', '创建失败：' . $e->getMessage());
            $this->redirect('index.php?controller=sale&action=create');
        }
    }

    /**
     * 查看销售详情
     */
    public function detail()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $sale = $this->saleModel->getWithItems($id);
        
        if (!$sale) {
            $this->setFlash('error', '销售记录不存在');
            $this->redirect('index.php?controller=sale&action=index');
        }

        $this->view('sales/view', [
            'title' => '销售详情',
            'sale' => $sale
        ]);
    }

    /**
     * 删除销售记录
     */
    public function delete()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $sale = $this->saleModel->getById($id);
        
        if (!$sale) {
            $this->setFlash('error', '销售记录不存在');
            $this->redirect('index.php?controller=sale&action=index');
        }

        try {
            $this->saleModel->delete($id);
            $this->setFlash('success', '销售记录删除成功，库存和客户余额已恢复');
        } catch (Exception $e) {
            $this->setFlash('error', '删除失败：' . $e->getMessage());
        }
        
        $this->redirect('index.php?controller=sale&action=index');
    }

    /**
     * 获取产品信息（AJAX接口）
     */
    public function getProductInfo()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $productId = $_POST['product_id'] ?? 0;
        $product = $this->productModel->getById($productId);
        
        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }

        header('Content-Type: application/json');
        echo json_encode([
            'id' => $product['id'],
            'name' => $product['name'],
            'unit' => $product['unit'],
            'stock_quantity' => $product['stock_quantity'],
            'selling_price' => $product['selling_price']
        ]);
    }

    /**
     * 打印销售单
     */
    public function print()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $sale = $this->saleModel->getWithItems($id);
        
        if (!$sale) {
            $this->setFlash('error', '销售记录不存在');
            $this->redirect('index.php?controller=sale&action=index');
        }

        $this->view('sales/print', [
            'title' => '打印销售单',
            'sale' => $sale
        ]);
    }
}
