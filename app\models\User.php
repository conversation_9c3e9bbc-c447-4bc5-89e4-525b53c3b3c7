<?php
/**
 * 用户模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class User
{
    private $db;
    private $table = 'users';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 根据用户名查找用户
     */
    public function findByUsername($username)
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ? LIMIT 1";
        return $this->db->fetch($sql, [$username]);
    }

    /**
     * 根据ID查找用户
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? LIMIT 1";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 创建新用户
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (username, password_hash, name, created_at) VALUES (?, ?, ?, NOW())";
        
        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $params = [
            $data['username'],
            $passwordHash,
            $data['name']
        ];
        
        $this->db->execute($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * 验证用户密码
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }

    /**
     * 更新用户信息
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [];
        
        if (isset($data['name'])) {
            $fields[] = 'name = ?';
            $params[] = $data['name'];
        }
        
        if (isset($data['password'])) {
            $fields[] = 'password_hash = ?';
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";
        
        return $this->db->execute($sql, $params) > 0;
    }

    /**
     * 删除用户
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->execute($sql, [$id]) > 0;
    }

    /**
     * 获取所有用户
     */
    public function getAll()
    {
        $sql = "SELECT id, username, name, created_at FROM {$this->table} ORDER BY created_at DESC";
        return $this->db->fetchAll($sql);
    }

    /**
     * 检查用户名是否已存在
     */
    public function usernameExists($username, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
}
