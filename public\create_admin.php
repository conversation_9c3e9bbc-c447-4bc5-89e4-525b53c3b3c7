<?php
/**
 * 创建管理员账号脚本
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../app/core/Database.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $name = trim($_POST['name'] ?? '');
    
    if (empty($username) || empty($password) || empty($name)) {
        $error = '所有字段都是必填的';
    } elseif (strlen($password) < 6) {
        $error = '密码长度至少6位';
    } else {
        try {
            $db = Database::getInstance();
            
            // 检查用户名是否已存在
            $existing = $db->fetch("SELECT id FROM users WHERE username = ?", [$username]);
            if ($existing) {
                $error = '用户名已存在';
            } else {
                // 创建管理员账号
                $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                $db->execute(
                    "INSERT INTO users (username, password_hash, name) VALUES (?, ?, ?)",
                    [$username, $passwordHash, $name]
                );
                $message = '管理员账号创建成功！';
            }
        } catch (Exception $e) {
            $error = '创建失败: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建管理员账号 - 麻糍工厂销售系统</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <h2 class="login-title">🧁 创建管理员账号</h2>
                <p class="text-muted">麻糍工厂销售系统</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <hr>
                    <a href="index.php" class="btn btn-success">立即登录</a>
                </div>
            <?php else: ?>
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required 
                               placeholder="请输入用户名" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                        <div class="form-text">用于登录系统的用户名</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required 
                               placeholder="请输入密码（至少6位）" minlength="6">
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="请输入真实姓名" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                        <div class="form-text">显示在系统中的姓名</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-user-plus"></i> 创建管理员账号
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="text-center mt-4">
                <a href="index.php" class="text-muted">返回登录页面</a>
            </div>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    &copy; <?php echo date('Y'); ?> 麻糍工厂销售系统
                </small>
            </div>
        </div>
    </div>
    
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
</body>
</html>
