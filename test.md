你是一名web应用开发领域的专家
我是一名编程新手 懂一点点的php 和 python , 我也不拒绝尝试其他的开发语言。
我在本机已创建了数据库 test2  用户名密码为  hrf99/qq111111

我希望搭建一套精简的进销存系统
1.
用户需要登录才能使用
2.
成品和原材料
    配方 麻糍皮 = 糯米粉500g+淀粉80g+白糖20g+炼乳10g
    糯米粉  每袋 500g
    淀粉    每袋 250g
    白糖    每袋 1000g

    配方的合成统一用g 来计算。
    商品允许负库存
    我希望 销售成品后 自动计算耗材的使用量，并且减少相应库存。我知道可能不准确，但是这样比较方便。有偏差的时候 我可以进行库存调整。

    成品的种类很少，只有大概5.6种，未来即使增加也不会太多
    耗材的种类大概20-30种。
3.
销售客户是可以欠款的，原料的采购都是现结算的。
我需要清楚的计算销售客户的往来账目。
销售订单由于商品种类很少，所以我希望可以方便快捷的录入单据 比如固定好品类，只输入数量就可以。
我关注的是采购成本，销售账目，客户欠款。
4.这个应用需要方便的在手机端使用，请设计简约清爽的界面。

----------------------------
请在public目录下生成一个调试目录 debug  ,制作一个固定的调试页面，不同的TAB 防止不同的调试功能，这样只要一个调试页面进行整个系统需要功能的调试数据。这样方便一些。这个页面要求只有管理员登录才能访问。
