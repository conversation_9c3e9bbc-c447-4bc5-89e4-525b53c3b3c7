<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit"></i> <?php echo $title; ?></h2>
    <a href="index.php?controller=inventory&action=index" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回库存管理
    </a>
</div>

<!-- 调整表单 -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit"></i> 库存调整
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=inventory&action=doAdjust">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <!-- 物品选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="item_type" class="form-label">
                                    <i class="fas fa-tags"></i> 物品类型 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="item_type" name="item_type" required>
                                    <option value="material" <?php echo $itemType === 'material' ? 'selected' : ''; ?>>原材料</option>
                                    <option value="product" <?php echo $itemType === 'product' ? 'selected' : ''; ?>>成品</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="item_id" class="form-label">
                                    <i class="fas fa-cube"></i> 选择物品 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="item_id" name="item_id" required>
                                    <option value="">请选择物品</option>
                                    <?php foreach ($items as $itemOption): ?>
                                        <option value="<?php echo $itemOption['id']; ?>" 
                                                data-stock="<?php echo $itemOption['stock_quantity']; ?>"
                                                data-unit="<?php echo htmlspecialchars($itemOption['unit']); ?>"
                                                data-price="<?php echo $itemType === 'material' ? $itemOption['unit_price'] : $itemOption['selling_price']; ?>"
                                                <?php echo ($item && $item['id'] == $itemOption['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($itemOption['name']); ?>
                                            <?php if ($itemOption['specification']): ?>
                                                (<?php echo htmlspecialchars($itemOption['specification']); ?>)
                                            <?php endif; ?>
                                            - 库存: <?php echo number_format($itemOption['stock_quantity'], 3); ?> <?php echo $itemOption['unit']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 当前库存信息 -->
                    <div class="row mb-4" id="current-stock-info" style="display: none;">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 当前库存信息</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>物品名称:</strong>
                                        <div id="current-name">-</div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>当前库存:</strong>
                                        <div id="current-stock">-</div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>单位:</strong>
                                        <div id="current-unit">-</div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>单价:</strong>
                                        <div id="current-price">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 调整设置 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="adjust_type" class="form-label">
                                    <i class="fas fa-cogs"></i> 调整方式 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="adjust_type" name="adjust_type" required>
                                    <option value="set">设置为指定数量</option>
                                    <option value="add">增加库存</option>
                                    <option value="subtract">减少库存</option>
                                </select>
                                <div class="form-text">选择库存调整的方式</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-calculator"></i> 数量 <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="0" step="0.001" placeholder="0.000" required>
                                <div class="form-text">输入调整的数量</div>
                            </div>
                        </div>
                    </div>

                    <!-- 调整预览 -->
                    <div class="row mb-4" id="adjust-preview" style="display: none;">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-eye"></i> 调整预览</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>调整前库存:</strong>
                                        <div id="preview-before">-</div>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>调整数量:</strong>
                                        <div id="preview-change">-</div>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>调整后库存:</strong>
                                        <div id="preview-after">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 调整原因 -->
                    <div class="mb-4">
                        <label for="reason" class="form-label">
                            <i class="fas fa-comment"></i> 调整原因
                        </label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="请输入库存调整的原因（可选）"></textarea>
                        <div class="form-text">记录此次调整的原因，便于后续查询</div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-between">
                        <a href="index.php?controller=inventory&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                            <i class="fas fa-save"></i> 确认调整
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 调整说明
                </h6>
                <ul class="mb-0">
                    <li><strong>设置为指定数量:</strong> 将库存直接设置为输入的数量</li>
                    <li><strong>增加库存:</strong> 在当前库存基础上增加指定数量</li>
                    <li><strong>减少库存:</strong> 在当前库存基础上减少指定数量</li>
                    <li>所有调整都会记录到库存变动日志中</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-exclamation-triangle text-danger"></i> 注意事项
                </h6>
                <ul class="mb-0">
                    <li>调整后的库存不能为负数</li>
                    <li>请确认调整数量的准确性</li>
                    <li>建议填写调整原因便于追溯</li>
                    <li>调整操作不可撤销，请谨慎操作</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const itemTypeSelect = document.getElementById('item_type');
    const itemIdSelect = document.getElementById('item_id');
    const adjustTypeSelect = document.getElementById('adjust_type');
    const quantityInput = document.getElementById('quantity');
    const submitBtn = document.getElementById('submit-btn');
    
    // 物品类型改变时重新加载物品列表
    itemTypeSelect.addEventListener('change', function() {
        window.location.href = 'index.php?controller=inventory&action=adjust&type=' + this.value;
    });
    
    // 物品选择改变时显示当前库存信息
    itemIdSelect.addEventListener('change', function() {
        const option = this.options[this.selectedIndex];
        if (option.value) {
            const stock = parseFloat(option.dataset.stock);
            const unit = option.dataset.unit;
            const price = parseFloat(option.dataset.price);
            
            document.getElementById('current-name').textContent = option.text.split(' - ')[0];
            document.getElementById('current-stock').textContent = stock.toFixed(3) + ' ' + unit;
            document.getElementById('current-unit').textContent = unit;
            document.getElementById('current-price').textContent = '¥' + price.toFixed(2);
            
            document.getElementById('current-stock-info').style.display = 'block';
            updatePreview();
        } else {
            document.getElementById('current-stock-info').style.display = 'none';
            document.getElementById('adjust-preview').style.display = 'none';
            submitBtn.disabled = true;
        }
    });
    
    // 调整方式或数量改变时更新预览
    adjustTypeSelect.addEventListener('change', updatePreview);
    quantityInput.addEventListener('input', updatePreview);
    
    function updatePreview() {
        const itemOption = itemIdSelect.options[itemIdSelect.selectedIndex];
        const adjustType = adjustTypeSelect.value;
        const quantity = parseFloat(quantityInput.value) || 0;
        
        if (!itemOption.value || quantity <= 0) {
            document.getElementById('adjust-preview').style.display = 'none';
            submitBtn.disabled = true;
            return;
        }
        
        const currentStock = parseFloat(itemOption.dataset.stock);
        const unit = itemOption.dataset.unit;
        let newStock = currentStock;
        let change = 0;
        
        switch (adjustType) {
            case 'set':
                newStock = quantity;
                change = quantity - currentStock;
                break;
            case 'add':
                newStock = currentStock + quantity;
                change = quantity;
                break;
            case 'subtract':
                newStock = currentStock - quantity;
                change = -quantity;
                break;
        }
        
        if (newStock < 0) {
            document.getElementById('adjust-preview').className = 'alert alert-danger';
            submitBtn.disabled = true;
        } else {
            document.getElementById('adjust-preview').className = 'alert alert-warning';
            submitBtn.disabled = false;
        }
        
        document.getElementById('preview-before').textContent = currentStock.toFixed(3) + ' ' + unit;
        document.getElementById('preview-change').textContent = (change > 0 ? '+' : '') + change.toFixed(3) + ' ' + unit;
        document.getElementById('preview-after').textContent = newStock.toFixed(3) + ' ' + unit;
        
        document.getElementById('adjust-preview').style.display = 'block';
    }
    
    // 如果有预选物品，触发change事件
    if (itemIdSelect.value) {
        itemIdSelect.dispatchEvent(new Event('change'));
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
