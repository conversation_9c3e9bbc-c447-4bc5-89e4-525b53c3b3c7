<?php
/**
 * 原材料控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Material.php';

class MaterialController extends Controller
{
    private $materialModel;

    public function __construct()
    {
        parent::__construct();
        $this->materialModel = new Material();
    }

    /**
     * 原材料列表页
     */
    public function index()
    {
        $this->requireAuth();

        $search = $_GET['search'] ?? '';

        if (!empty($search)) {
            $materials = $this->materialModel->search($search);
        } else {
            $materials = $this->materialModel->getAll();
        }

        $stats = $this->materialModel->getStats();

        $this->view('materials/index', [
            'title' => '原材料管理',
            'materials' => $materials,
            'stats' => $stats,
            'search' => $search
        ]);
    }

    /**
     * 显示新增原材料页面
     */
    public function create()
    {
        $this->requireAuth();

        $this->view('materials/create', [
            'title' => '添加原材料',
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理新增原材料请求
     */
    public function store()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=material&action=index');
        }

        $this->validateCsrfToken();

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '原材料名称不能为空且不超过100个字符'
            ],
            'unit' => [
                'required' => true,
                'max_length' => 20,
                'message' => '单位不能为空且不超过20个字符'
            ]
        ]);

        // 检查名称是否已存在
        if (empty($errors) && $this->materialModel->nameExists($_POST['name'])) {
            $errors['name'] = '该原材料名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=material&action=create');
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'specification' => trim($_POST['specification'] ?? ''),
                'unit' => trim($_POST['unit']),
                'stock_quantity' => floatval($_POST['stock_quantity'] ?? 0),
                'unit_price' => !empty($_POST['unit_price']) ? floatval($_POST['unit_price']) : null
            ];

            $id = $this->materialModel->create($data);
            
            $this->setFlash('success', '原材料添加成功');
            $this->redirect('index.php?controller=material&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '添加失败：' . $e->getMessage());
            $this->redirect('index.php?controller=material&action=create');
        }
    }

    /**
     * 显示编辑原材料页面
     */
    public function edit()
    {
        $id = $_GET['id'] ?? 0;
        $material = $this->materialModel->getById($id);
        
        if (!$material) {
            $this->setFlash('error', '原材料不存在');
            $this->redirect('index.php?controller=material&action=index');
        }

        $this->view('materials/edit', [
            'title' => '编辑原材料',
            'material' => $material,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理更新原材料请求
     */
    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=material&action=index');
        }

        $this->validateCsrfToken();

        $id = $_POST['id'] ?? 0;
        $material = $this->materialModel->getById($id);
        
        if (!$material) {
            $this->setFlash('error', '原材料不存在');
            $this->redirect('index.php?controller=material&action=index');
        }

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '原材料名称不能为空且不超过100个字符'
            ],
            'unit' => [
                'required' => true,
                'max_length' => 20,
                'message' => '单位不能为空且不超过20个字符'
            ]
        ]);

        // 检查名称是否已存在（排除当前记录）
        if (empty($errors) && $this->materialModel->nameExists($_POST['name'], $id)) {
            $errors['name'] = '该原材料名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=material&action=edit&id=' . $id);
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'specification' => trim($_POST['specification'] ?? ''),
                'unit' => trim($_POST['unit']),
                'stock_quantity' => floatval($_POST['stock_quantity'] ?? 0),
                'unit_price' => !empty($_POST['unit_price']) ? floatval($_POST['unit_price']) : null
            ];

            $this->materialModel->update($id, $data);
            
            $this->setFlash('success', '原材料更新成功');
            $this->redirect('index.php?controller=material&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '更新失败：' . $e->getMessage());
            $this->redirect('index.php?controller=material&action=edit&id=' . $id);
        }
    }

    /**
     * 删除原材料
     */
    public function delete()
    {
        $id = $_GET['id'] ?? 0;
        $material = $this->materialModel->getById($id);
        
        if (!$material) {
            $this->setFlash('error', '原材料不存在');
            $this->redirect('index.php?controller=material&action=index');
        }

        try {
            $this->materialModel->delete($id);
            $this->setFlash('success', '原材料删除成功');
        } catch (Exception $e) {
            $this->setFlash('error', '删除失败：' . $e->getMessage());
        }
        
        $this->redirect('index.php?controller=material&action=index');
    }

    /**
     * 查看原材料详情
     */
    public function detail()
    {
        $id = $_GET['id'] ?? 0;
        $material = $this->materialModel->getById($id);

        if (!$material) {
            $this->setFlash('error', '原材料不存在');
            $this->redirect('index.php?controller=material&action=index');
        }

        $this->view('materials/view', [
            'title' => '原材料详情',
            'material' => $material
        ]);
    }
}
