<?php
/**
 * 认证逻辑测试
 */

session_start();

echo "<h2>认证逻辑测试</h2>";

echo "<h3>会话信息</h3>";
echo "会话ID: " . session_id() . "<br>";
echo "用户ID: " . ($_SESSION['user_id'] ?? '未设置') . "<br>";
echo "用户名: " . ($_SESSION['username'] ?? '未设置') . "<br>";

echo "<h3>详细会话数据</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>测试认证逻辑</h3>";

// 模拟Controller的认证逻辑
function testIsLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

$isLoggedIn = testIsLoggedIn();
echo "isLoggedIn() 结果: " . ($isLoggedIn ? 'true' : 'false') . "<br>";

if ($isLoggedIn) {
    echo "✅ 认证检查通过<br>";
} else {
    echo "❌ 认证检查失败<br>";
    echo "原因分析：<br>";
    
    if (!isset($_SESSION['user_id'])) {
        echo "- \$_SESSION['user_id'] 未设置<br>";
    } elseif (empty($_SESSION['user_id'])) {
        echo "- \$_SESSION['user_id'] 为空值<br>";
    }
}

echo "<h3>测试Controller基类</h3>";
try {
    require_once '../app/core/Controller.php';
    
    // 创建一个简单的测试控制器
    class TestController extends Controller {
        public function testAuth() {
            return $this->isLoggedIn();
        }
        
        public function testRequireAuth() {
            $this->requireAuth();
            return "认证通过";
        }
    }
    
    $testController = new TestController();
    echo "✅ Controller基类加载成功<br>";
    
    $authResult = $testController->testAuth();
    echo "Controller::isLoggedIn() 结果: " . ($authResult ? 'true' : 'false') . "<br>";
    
    if ($authResult) {
        echo "开始测试requireAuth()...<br>";
        $requireAuthResult = $testController->testRequireAuth();
        echo "✅ requireAuth() 测试通过: " . $requireAuthResult . "<br>";
    } else {
        echo "❌ Controller认证失败，不测试requireAuth()<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Controller测试失败: " . $e->getMessage() . "<br>";
}

echo "<h3>PHP环境信息</h3>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "会话保存路径: " . session_save_path() . "<br>";
echo "会话名称: " . session_name() . "<br>";
echo "会话状态: " . session_status() . " (1=disabled, 2=active)<br>";

echo "<h3>建议</h3>";
if (!$isLoggedIn) {
    echo "请先<a href='index.php?controller=auth&action=login'>登录</a><br>";
} else {
    echo "认证正常，可以继续测试MaterialController<br>";
    echo '<a href="material_direct.php">测试MaterialController</a><br>';
}
?>
