<?php
/**
 * 麻糍工厂销售系统 - 主入口文件
 * 前端控制器，处理所有请求的路由分发
 */

// 启动会话
session_start();

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 获取请求参数
$controller = $_GET['controller'] ?? 'auth';
$action = $_GET['action'] ?? 'login';

// 控制器映射
$controllerMap = [
    'auth' => 'AuthController',
    'home' => 'HomeController',
    'product' => 'ProductController',
    'material' => 'MaterialController',
    'recipe' => 'RecipeController',
    'inventory' => 'InventoryController',
    'sale' => 'SaleController',
    'customer' => 'CustomerController',
    'user' => 'UserController'
];

// 验证控制器
if (!isset($controllerMap[$controller])) {
    $controller = 'auth';
    $action = 'login';
}

$controllerClass = $controllerMap[$controller];
$controllerFile = __DIR__ . '/../app/controllers/' . $controllerClass . '.php';

// 检查控制器文件是否存在
if (!file_exists($controllerFile)) {
    die("控制器文件不存在: {$controllerClass}");
}

// 引入控制器文件
require_once $controllerFile;

// 检查控制器类是否存在
if (!class_exists($controllerClass)) {
    die("控制器类不存在: {$controllerClass}");
}

// 实例化控制器
try {
    $controllerInstance = new $controllerClass();
    
    // 检查方法是否存在
    if (!method_exists($controllerInstance, $action)) {
        die("方法不存在: {$controllerClass}::{$action}");
    }
    
    // 调用控制器方法
    $controllerInstance->$action();
    
} catch (Exception $e) {
    // 错误处理
    error_log("系统错误: " . $e->getMessage());
    
    // 如果是开发环境，显示详细错误
    if (ini_get('display_errors')) {
        die("系统错误: " . $e->getMessage());
    } else {
        // 生产环境显示友好错误页面
        die("系统暂时不可用，请稍后再试。");
    }
}
