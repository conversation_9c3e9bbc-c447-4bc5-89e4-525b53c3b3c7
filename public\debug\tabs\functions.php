<?php
/**
 * 功能测试标签
 */

session_start();

// 定义测试模块
$testModules = [
    'auth' => [
        'name' => '用户认证',
        'icon' => 'fa-user-shield',
        'tests' => [
            'login' => '登录功能',
            'logout' => '退出功能',
            'session' => '会话管理',
            'permissions' => '权限检查'
        ]
    ],
    'materials' => [
        'name' => '原材料管理',
        'icon' => 'fa-cubes',
        'tests' => [
            'create' => '创建原材料',
            'read' => '查看原材料',
            'update' => '更新原材料',
            'delete' => '删除原材料',
            'search' => '搜索功能',
            'stock' => '库存管理'
        ]
    ],
    'products' => [
        'name' => '成品管理',
        'icon' => 'fa-box',
        'tests' => [
            'create' => '创建成品',
            'read' => '查看成品',
            'update' => '更新成品',
            'delete' => '删除成品',
            'search' => '搜索功能',
            'stock' => '库存管理'
        ]
    ],
    'customers' => [
        'name' => '客户管理',
        'icon' => 'fa-users',
        'tests' => [
            'create' => '创建客户',
            'read' => '查看客户',
            'update' => '更新客户',
            'delete' => '删除客户',
            'search' => '搜索功能',
            'balance' => '余额管理'
        ]
    ],
    'recipes' => [
        'name' => '配方管理',
        'icon' => 'fa-list-ul',
        'tests' => [
            'create' => '创建配方',
            'read' => '查看配方',
            'update' => '更新配方',
            'delete' => '删除配方',
            'items' => '配方项目管理',
            'calculation' => '成本计算'
        ]
    ],
    'sales' => [
        'name' => '销售管理',
        'icon' => 'fa-shopping-cart',
        'tests' => [
            'create' => '创建销售',
            'read' => '查看销售',
            'update' => '更新销售',
            'delete' => '删除销售',
            'items' => '销售项目管理',
            'payment' => '付款处理'
        ]
    ],
    'inventory' => [
        'name' => '库存管理',
        'icon' => 'fa-warehouse',
        'tests' => [
            'adjust' => '库存调整',
            'logs' => '变动记录',
            'alerts' => '库存预警',
            'reports' => '库存报表'
        ]
    ]
];
?>

<div class="row">
    <!-- 测试控制面板 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-play-circle"></i> 测试控制</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="runAllTests()">
                        <i class="fas fa-play"></i> 运行所有测试
                    </button>
                    <button class="btn btn-info" onclick="runQuickTests()">
                        <i class="fas fa-bolt"></i> 快速测试
                    </button>
                    <button class="btn btn-warning" onclick="clearTestResults()">
                        <i class="fas fa-eraser"></i> 清空结果
                    </button>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <label class="form-label">测试模式:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="testMode" id="mode-safe" value="safe" checked>
                        <label class="form-check-label" for="mode-safe">
                            安全模式 (只读测试)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="testMode" id="mode-full" value="full">
                        <label class="form-check-label" for="mode-full">
                            完整模式 (包含写入测试)
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="test-iterations" class="form-label">测试次数:</label>
                    <select class="form-control" id="test-iterations">
                        <option value="1" selected>1次</option>
                        <option value="3">3次</option>
                        <option value="5">5次</option>
                        <option value="10">10次</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 测试统计 -->
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> 测试统计</h6>
            </div>
            <div class="card-body">
                <div id="test-stats">
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-bar fa-2x"></i>
                        <p class="mt-2">暂无测试数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试模块列表 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-list"></i> 功能模块测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($testModules as $moduleKey => $module): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas <?php echo $module['icon']; ?>"></i>
                                    <?php echo $module['name']; ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <button class="btn btn-outline-primary btn-sm" 
                                            onclick="runModuleTests('<?php echo $moduleKey; ?>')">
                                        <i class="fas fa-play"></i> 测试模块
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="showModuleDetails('<?php echo $moduleKey; ?>')">
                                        <i class="fas fa-info"></i> 详情
                                    </button>
                                </div>
                                
                                <div class="test-items">
                                    <?php foreach ($module['tests'] as $testKey => $testName): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small><?php echo $testName; ?></small>
                                        <button class="btn btn-outline-secondary btn-xs" 
                                                onclick="runSingleTest('<?php echo $moduleKey; ?>', '<?php echo $testKey; ?>')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试结果显示区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> 测试结果</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-light" onclick="exportTestResults()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-light" onclick="shareTestResults()">
                            <i class="fas fa-share"></i> 分享
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="test-results">
                    <div class="text-center text-muted">
                        <i class="fas fa-clipboard fa-2x"></i>
                        <p class="mt-2">点击上方按钮开始测试</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块详情模态框 -->
<div class="modal fade" id="moduleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模块测试详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="module-detail-content">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>

<script>
// 测试结果存储
let testResults = [];
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
};

// 运行所有测试
function runAllTests() {
    const mode = document.querySelector('input[name="testMode"]:checked').value;
    const iterations = document.getElementById('test-iterations').value;
    
    showTestLoading('正在运行所有功能测试...');
    
    fetch('../actions/run_all_tests.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            mode: mode,
            iterations: parseInt(iterations)
        })
    })
    .then(response => response.json())
    .then(data => {
        displayTestResults(data);
        updateTestStats(data);
    })
    .catch(error => {
        showTestError('测试执行失败: ' + error.message);
    });
}

// 运行快速测试
function runQuickTests() {
    showTestLoading('正在运行快速测试...');
    
    fetch('../actions/run_quick_tests.php')
        .then(response => response.json())
        .then(data => {
            displayTestResults(data);
            updateTestStats(data);
        })
        .catch(error => {
            showTestError('快速测试失败: ' + error.message);
        });
}

// 运行模块测试
function runModuleTests(module) {
    const mode = document.querySelector('input[name="testMode"]:checked').value;
    
    showTestLoading(`正在测试 ${module} 模块...`);
    
    fetch('../actions/run_module_tests.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            module: module,
            mode: mode
        })
    })
    .then(response => response.json())
    .then(data => {
        displayTestResults(data);
        updateTestStats(data);
    })
    .catch(error => {
        showTestError(`${module} 模块测试失败: ` + error.message);
    });
}

// 运行单个测试
function runSingleTest(module, test) {
    const mode = document.querySelector('input[name="testMode"]:checked').value;
    
    showTestLoading(`正在测试 ${module}.${test}...`);
    
    fetch('../actions/run_single_test.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            module: module,
            test: test,
            mode: mode
        })
    })
    .then(response => response.json())
    .then(data => {
        displayTestResults(data);
        updateTestStats(data);
    })
    .catch(error => {
        showTestError(`${module}.${test} 测试失败: ` + error.message);
    });
}

// 显示模块详情
function showModuleDetails(module) {
    fetch('../actions/module_details.php?module=' + module)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayModuleDetails(module, data.data);
            } else {
                alert('获取模块详情失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('获取模块详情失败: ' + error.message);
        });
}

// 显示测试加载状态
function showTestLoading(message) {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary"></div>
            <p class="mt-2">${message}</p>
        </div>
    `;
}

// 显示测试结果
function displayTestResults(data) {
    const resultDiv = document.getElementById('test-results');
    
    if (!data.success) {
        showTestError(data.message);
        return;
    }
    
    const results = data.results || [];
    testResults = testResults.concat(results);
    
    let html = '<div class="test-results-container">';
    
    // 按模块分组显示结果
    const groupedResults = {};
    results.forEach(result => {
        if (!groupedResults[result.module]) {
            groupedResults[result.module] = [];
        }
        groupedResults[result.module].push(result);
    });
    
    Object.keys(groupedResults).forEach(module => {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-folder"></i> ${module}</h6>
                <div class="test-module-results">
        `;
        
        groupedResults[module].forEach(result => {
            const statusClass = result.status === 'pass' ? 'test-pass' : 
                               result.status === 'fail' ? 'test-fail' : 'test-warning';
            const icon = result.status === 'pass' ? 'fa-check' : 
                        result.status === 'fail' ? 'fa-times' : 'fa-exclamation-triangle';
            
            html += `
                <div class="test-result ${statusClass}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas ${icon}"></i>
                            <strong>${result.test}</strong>
                            <span class="ms-2">${result.message}</span>
                        </div>
                        <div>
                            <small class="text-muted">${result.duration}ms</small>
                            ${result.details ? '<button class="btn btn-sm btn-outline-secondary ms-1" onclick="showTestDetails(\'' + result.id + '\')"><i class="fas fa-info"></i></button>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div></div>';
    });
    
    html += '</div>';
    
    // 添加时间戳
    html += `<div class="text-end mt-3"><small class="text-muted">测试时间: ${new Date().toLocaleString()}</small></div>`;
    
    resultDiv.innerHTML = html;
}

// 更新测试统计
function updateTestStats(data) {
    if (!data.success || !data.results) return;
    
    data.results.forEach(result => {
        testStats.total++;
        switch (result.status) {
            case 'pass':
                testStats.passed++;
                break;
            case 'fail':
                testStats.failed++;
                break;
            case 'warning':
                testStats.warnings++;
                break;
        }
    });
    
    const statsDiv = document.getElementById('test-stats');
    const successRate = testStats.total > 0 ? (testStats.passed / testStats.total * 100).toFixed(1) : 0;
    
    statsDiv.innerHTML = `
        <div class="row text-center">
            <div class="col-6">
                <h4 class="text-primary">${testStats.total}</h4>
                <small>总测试数</small>
            </div>
            <div class="col-6">
                <h4 class="text-success">${successRate}%</h4>
                <small>成功率</small>
            </div>
        </div>
        <hr>
        <div class="row text-center">
            <div class="col-4">
                <div class="text-success">
                    <strong>${testStats.passed}</strong><br>
                    <small>通过</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-warning">
                    <strong>${testStats.warnings}</strong><br>
                    <small>警告</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-danger">
                    <strong>${testStats.failed}</strong><br>
                    <small>失败</small>
                </div>
            </div>
        </div>
    `;
}

// 清空测试结果
function clearTestResults() {
    testResults = [];
    testStats = { total: 0, passed: 0, failed: 0, warnings: 0 };
    
    document.getElementById('test-results').innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-clipboard fa-2x"></i>
            <p class="mt-2">测试结果已清空</p>
        </div>
    `;
    
    document.getElementById('test-stats').innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-chart-bar fa-2x"></i>
            <p class="mt-2">暂无测试数据</p>
        </div>
    `;
}

// 导出测试结果
function exportTestResults() {
    if (testResults.length === 0) {
        alert('没有测试结果可导出');
        return;
    }
    
    const exportData = {
        timestamp: new Date().toISOString(),
        stats: testStats,
        results: testResults
    };
    
    window.debugUtils.downloadFile(
        JSON.stringify(exportData, null, 2),
        `test_results_${new Date().toISOString().slice(0,10)}.json`
    );
}

// 分享测试结果
function shareTestResults() {
    if (testResults.length === 0) {
        alert('没有测试结果可分享');
        return;
    }
    
    const summary = `测试结果摘要:
总测试数: ${testStats.total}
通过: ${testStats.passed}
警告: ${testStats.warnings}
失败: ${testStats.failed}
成功率: ${(testStats.passed / testStats.total * 100).toFixed(1)}%
测试时间: ${new Date().toLocaleString()}`;
    
    window.debugUtils.copyToClipboard(summary);
}

// 显示测试错误
function showTestError(message) {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> 测试失败</h6>
            <p>${message}</p>
        </div>
    `;
}

// 显示模块详情
function displayModuleDetails(module, details) {
    const modalContent = document.getElementById('module-detail-content');
    
    let html = `
        <h6>模块: ${module}</h6>
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>控制器:</td><td>${details.controller || '未知'}</td></tr>
                    <tr><td>模型:</td><td>${details.model || '未知'}</td></tr>
                    <tr><td>视图:</td><td>${details.views || '未知'}</td></tr>
                    <tr><td>路由:</td><td>${details.routes || '未知'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>测试覆盖</h6>
                <div class="progress mb-2">
                    <div class="progress-bar" style="width: ${details.coverage || 0}%">${details.coverage || 0}%</div>
                </div>
                <small class="text-muted">测试覆盖率</small>
            </div>
        </div>
    `;
    
    if (details.dependencies) {
        html += `
            <h6 class="mt-3">依赖关系</h6>
            <ul class="list-group list-group-flush">
        `;
        details.dependencies.forEach(dep => {
            html += `<li class="list-group-item">${dep}</li>`;
        });
        html += '</ul>';
    }
    
    modalContent.innerHTML = html;
    new bootstrap.Modal(document.getElementById('moduleDetailModal')).show();
}
</script>
