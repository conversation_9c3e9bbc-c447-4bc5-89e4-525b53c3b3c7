<?php
/**
 * 成品模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class Product
{
    private $db;
    private $table = 'products';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取所有成品
     */
    public function getAll($orderBy = 'name', $order = 'ASC')
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY {$orderBy} {$order}";
        return $this->db->fetchAll($sql);
    }

    /**
     * 根据ID获取成品
     */
    public function getById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? LIMIT 1";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 创建新成品
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (name, specification, unit, stock_quantity, selling_price, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())";

        $params = [
            $data['name'],
            $data['specification'] ?? null,
            $data['unit'],
            $data['stock_quantity'] ?? 0,
            $data['selling_price']
        ];

        $this->db->execute($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * 更新成品
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [];
        
        if (isset($data['name'])) {
            $fields[] = 'name = ?';
            $params[] = $data['name'];
        }
        
        if (isset($data['specification'])) {
            $fields[] = 'specification = ?';
            $params[] = $data['specification'];
        }
        
        if (isset($data['unit'])) {
            $fields[] = 'unit = ?';
            $params[] = $data['unit'];
        }
        
        if (isset($data['stock_quantity'])) {
            $fields[] = 'stock_quantity = ?';
            $params[] = $data['stock_quantity'];
        }
        
        if (isset($data['selling_price'])) {
            $fields[] = 'selling_price = ?';
            $params[] = $data['selling_price'];
        }
        
        if (empty($fields)) {
            return false;
        }

        // 添加更新时间
        $fields[] = 'updated_at = NOW()';
        $params[] = $id;
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";

        return $this->db->execute($sql, $params) > 0;
    }

    /**
     * 删除成品
     */
    public function delete($id)
    {
        // 检查是否有配方
        $hasRecipe = $this->db->fetch("SELECT COUNT(*) as count FROM recipes WHERE product_id = ?", [$id]);
        if ($hasRecipe['count'] > 0) {
            throw new Exception('该成品有关联的配方，请先删除配方');
        }
        
        // 检查是否被销售订单使用（后续实现销售功能时添加）
        
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->execute($sql, [$id]) > 0;
    }

    /**
     * 检查名称是否已存在
     */
    public function nameExists($name, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }

    /**
     * 更新库存数量
     */
    public function updateStock($id, $quantity, $operation = 'set')
    {
        if ($operation === 'add') {
            $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity + ? WHERE id = ?";
        } elseif ($operation === 'subtract') {
            $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity - ? WHERE id = ?";
        } else {
            $sql = "UPDATE {$this->table} SET stock_quantity = ? WHERE id = ?";
        }
        
        return $this->db->execute($sql, [$quantity, $id]) > 0;
    }

    /**
     * 获取库存不足的成品
     */
    public function getLowStock($threshold = 10)
    {
        $sql = "SELECT * FROM {$this->table} WHERE stock_quantity < ? ORDER BY stock_quantity ASC";
        return $this->db->fetchAll($sql, [$threshold]);
    }

    /**
     * 搜索成品
     */
    public function search($keyword)
    {
        $sql = "SELECT * FROM {$this->table} WHERE name LIKE ? OR specification LIKE ? ORDER BY name";
        $searchTerm = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    /**
     * 获取成品及其配方信息
     */
    public function getWithRecipe($id)
    {
        $product = $this->getById($id);
        if (!$product) {
            return null;
        }
        
        // 获取配方信息
        $recipe = $this->db->fetch("SELECT * FROM recipes WHERE product_id = ?", [$id]);
        if ($recipe) {
            // 获取配方明细
            $recipeItems = $this->db->fetchAll("
                SELECT ri.*, m.name as material_name, m.unit as material_unit, m.stock_quantity as material_stock
                FROM recipe_items ri 
                JOIN materials m ON ri.material_id = m.id 
                WHERE ri.recipe_id = ?
                ORDER BY m.name
            ", [$recipe['id']]);
            
            $product['recipe'] = $recipe;
            $product['recipe_items'] = $recipeItems;
        } else {
            $product['recipe'] = null;
            $product['recipe_items'] = [];
        }
        
        return $product;
    }

    /**
     * 获取统计信息
     */
    public function getStats()
    {
        $stats = [];
        
        // 总数量
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total_count'] = $result['count'];
        
        // 库存不足数量
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table} WHERE stock_quantity < 10");
        $stats['low_stock_count'] = $result['count'];
        
        // 总库存价值
        $result = $this->db->fetch("SELECT SUM(stock_quantity * selling_price) as total_value FROM {$this->table}");
        $stats['total_value'] = $result['total_value'] ?? 0;
        
        // 有配方的成品数量
        $result = $this->db->fetch("SELECT COUNT(DISTINCT product_id) as count FROM recipes");
        $stats['with_recipe_count'] = $result['count'];
        
        return $stats;
    }

    /**
     * 获取可用于销售的成品（有库存的）
     */
    public function getAvailableForSale()
    {
        $sql = "SELECT * FROM {$this->table} WHERE stock_quantity > 0 ORDER BY name";
        return $this->db->fetchAll($sql);
    }
}
