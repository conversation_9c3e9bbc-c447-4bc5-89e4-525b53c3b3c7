<?php
/**
 * 系统概览标签
 */

session_start();
require_once '../../../app/core/Database.php';
require_once '../../../app/core/SystemHealthChecker.php';

$db = Database::getInstance();
$healthChecker = new SystemHealthChecker();

// 执行快速检查
$quickCheck = $healthChecker->quickCheck();

// 获取系统统计
$stats = [];
try {
    $stats['users'] = $db->fetch("SELECT COUNT(*) as count FROM users")['count'];
    $stats['materials'] = $db->fetch("SELECT COUNT(*) as count FROM materials")['count'];
    $stats['products'] = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
    $stats['customers'] = $db->fetch("SELECT COUNT(*) as count FROM customers")['count'];
    $stats['recipes'] = $db->fetch("SELECT COUNT(*) as count FROM recipes")['count'];
    $stats['sales'] = $db->fetch("SELECT COUNT(*) as count FROM sales")['count'];
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}

// 获取系统信息
$systemInfo = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? '未知',
    'memory_limit' => ini_get('memory_limit'),
    'memory_usage' => number_format(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
    'peak_memory' => number_format(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB',
    'execution_time' => ini_get('max_execution_time') . '秒',
    'upload_limit' => ini_get('upload_max_filesize'),
    'timezone' => date_default_timezone_get(),
    'current_time' => date('Y-m-d H:i:s')
];

// 检查关键文件
$criticalFiles = [
    '../../app/core/Database.php' => '数据库核心',
    '../../app/core/Controller.php' => '控制器基类',
    '../../app/models/Material.php' => '原材料模型',
    '../../app/models/Product.php' => '成品模型',
    '../../app/models/Customer.php' => '客户模型',
    '../../app/models/Recipe.php' => '配方模型',
    '../../app/models/Sale.php' => '销售模型'
];

$fileStatus = [];
foreach ($criticalFiles as $file => $name) {
    $fileStatus[$name] = [
        'exists' => file_exists($file),
        'readable' => file_exists($file) && is_readable($file),
        'size' => file_exists($file) ? filesize($file) : 0,
        'modified' => file_exists($file) ? date('Y-m-d H:i:s', filemtime($file)) : null
    ];
}
?>

<div class="row">
    <!-- 系统状态卡片 -->
    <div class="col-md-6 mb-4">
        <div class="card status-card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-heartbeat"></i> 系统健康状态</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-3">
                            <i class="fas fa-database fa-2x <?php echo $quickCheck['database'] === 'pass' ? 'text-success' : 'text-danger'; ?>"></i>
                            <h6 class="mt-2">数据库</h6>
                            <span class="badge bg-<?php echo $quickCheck['database'] === 'pass' ? 'success' : 'danger'; ?>">
                                <?php echo $quickCheck['database'] === 'pass' ? '正常' : '异常'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <i class="fas fa-table fa-2x <?php echo $quickCheck['tables'] === 'pass' ? 'text-success' : 'text-danger'; ?>"></i>
                            <h6 class="mt-2">数据表</h6>
                            <span class="badge bg-<?php echo $quickCheck['tables'] === 'pass' ? 'success' : 'danger'; ?>">
                                <?php echo $quickCheck['tables'] === 'pass' ? '正常' : '异常'; ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-outline-primary btn-sm" onclick="runHealthCheck()">
                        <i class="fas fa-sync"></i> 完整检查
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计 -->
    <div class="col-md-6 mb-4">
        <div class="card status-card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> 数据统计</h5>
            </div>
            <div class="card-body">
                <?php if (isset($stats['error'])): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        数据库连接失败: <?php echo htmlspecialchars($stats['error']); ?>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-2">
                                <h4 class="text-primary"><?php echo $stats['users']; ?></h4>
                                <small>用户数</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-2">
                                <h4 class="text-info"><?php echo $stats['materials']; ?></h4>
                                <small>原材料</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-2">
                                <h4 class="text-success"><?php echo $stats['products']; ?></h4>
                                <small>成品</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-2">
                                <h4 class="text-warning"><?php echo $stats['customers']; ?></h4>
                                <small>客户</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-secondary"><?php echo $stats['recipes']; ?></h4>
                                <small>配方</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-dark"><?php echo $stats['sales']; ?></h4>
                                <small>销售记录</small>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 系统信息 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-server"></i> 系统信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>PHP版本:</strong></td>
                        <td><?php echo $systemInfo['php_version']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>服务器:</strong></td>
                        <td><?php echo $systemInfo['server_software']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>内存限制:</strong></td>
                        <td><?php echo $systemInfo['memory_limit']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>当前内存:</strong></td>
                        <td><?php echo $systemInfo['memory_usage']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>峰值内存:</strong></td>
                        <td><?php echo $systemInfo['peak_memory']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>时区:</strong></td>
                        <td><?php echo $systemInfo['timezone']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>当前时间:</strong></td>
                        <td><?php echo $systemInfo['current_time']; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- 关键文件状态 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-file-code"></i> 关键文件状态</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>文件</th>
                                <th>状态</th>
                                <th>大小</th>
                                <th>修改时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($fileStatus as $name => $status): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($name); ?></td>
                                <td>
                                    <?php if ($status['exists']): ?>
                                        <span class="badge bg-success">存在</span>
                                        <?php if (!$status['readable']): ?>
                                            <span class="badge bg-warning">不可读</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-danger">缺失</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $status['exists'] ? number_format($status['size'] / 1024, 1) . 'KB' : '-'; ?>
                                </td>
                                <td>
                                    <small><?php echo $status['modified'] ?? '-'; ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="testDatabaseConnection()">
                                <i class="fas fa-database"></i> 测试数据库
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-success" onclick="checkAllModels()">
                                <i class="fas fa-cogs"></i> 检查模型
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-info" onclick="generateTestData()">
                                <i class="fas fa-plus"></i> 生成测试数据
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-warning" onclick="clearCache()">
                                <i class="fas fa-trash"></i> 清理缓存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作结果显示区域 -->
<div id="operation-result" class="mt-3" style="display: none;"></div>

<script>
// 运行健康检查
function runHealthCheck() {
    showLoading('正在进行系统健康检查...');
    
    fetch('../actions/health_check.php')
        .then(response => response.json())
        .then(data => {
            showResult(data, '健康检查结果');
        })
        .catch(error => {
            showError('健康检查失败: ' + error.message);
        });
}

// 测试数据库连接
function testDatabaseConnection() {
    showLoading('正在测试数据库连接...');
    
    fetch('../actions/test_database.php')
        .then(response => response.json())
        .then(data => {
            showResult(data, '数据库连接测试');
        })
        .catch(error => {
            showError('数据库测试失败: ' + error.message);
        });
}

// 检查所有模型
function checkAllModels() {
    showLoading('正在检查所有模型...');
    
    fetch('../actions/check_models.php')
        .then(response => response.json())
        .then(data => {
            showResult(data, '模型检查结果');
        })
        .catch(error => {
            showError('模型检查失败: ' + error.message);
        });
}

// 生成测试数据
function generateTestData() {
    if (confirm('确定要生成测试数据吗？这可能会影响现有数据。')) {
        showLoading('正在生成测试数据...');
        
        fetch('../actions/generate_test_data.php', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                showResult(data, '测试数据生成结果');
                // 刷新统计数据
                setTimeout(() => location.reload(), 2000);
            })
            .catch(error => {
                showError('测试数据生成失败: ' + error.message);
            });
    }
}

// 清理缓存
function clearCache() {
    showLoading('正在清理缓存...');
    
    fetch('../actions/clear_cache.php', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            showResult(data, '缓存清理结果');
        })
        .catch(error => {
            showError('缓存清理失败: ' + error.message);
        });
}

// 显示加载状态
function showLoading(message) {
    const resultDiv = document.getElementById('operation-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <div class="alert alert-info">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2"></div>
                <div>${message}</div>
            </div>
        </div>
    `;
}

// 显示结果
function showResult(data, title) {
    const resultDiv = document.getElementById('operation-result');
    const alertClass = data.success ? 'alert-success' : 'alert-danger';
    const icon = data.success ? 'fa-check-circle' : 'fa-times-circle';
    
    let html = `
        <div class="alert ${alertClass}">
            <h6><i class="fas ${icon}"></i> ${title}</h6>
            <p>${data.message}</p>
    `;
    
    if (data.details) {
        html += '<div class="mt-2"><strong>详细信息:</strong><pre class="mt-1">' + 
                JSON.stringify(data.details, null, 2) + '</pre></div>';
    }
    
    html += '</div>';
    resultDiv.innerHTML = html;
}

// 显示错误
function showError(message) {
    const resultDiv = document.getElementById('operation-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> 操作失败</h6>
            <p>${message}</p>
        </div>
    `;
}
</script>
