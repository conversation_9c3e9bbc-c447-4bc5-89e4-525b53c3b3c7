<?php
/**
 * 添加原材料页面
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php?controller=auth&action=login');
    exit;
}

require_once '../app/core/Database.php';
require_once '../app/models/Material.php';

$materialModel = new Material();
$message = '';
$messageType = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $specification = trim($_POST['specification'] ?? '');
    $unit = trim($_POST['unit'] ?? '');
    $stock_quantity = floatval($_POST['stock_quantity'] ?? 0);
    $unit_price = !empty($_POST['unit_price']) ? floatval($_POST['unit_price']) : null;
    
    // 验证
    $errors = [];
    if (empty($name)) {
        $errors[] = '原材料名称不能为空';
    }
    if (empty($unit)) {
        $errors[] = '单位不能为空';
    }
    if ($materialModel->nameExists($name)) {
        $errors[] = '该原材料名称已存在';
    }
    
    if (empty($errors)) {
        try {
            $data = [
                'name' => $name,
                'specification' => $specification,
                'unit' => $unit,
                'stock_quantity' => $stock_quantity,
                'unit_price' => $unit_price
            ];
            
            $materialModel->create($data);
            header('Location: materials.php?success=1');
            exit;
            
        } catch (Exception $e) {
            $message = '添加失败：' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加原材料 - 麻糍工厂销售系统</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="index.php">🧁 麻糍工厂</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">欢迎，<?php echo htmlspecialchars($_SESSION['name']); ?></span>
                <a class="nav-link" href="materials.php">原材料管理</a>
                <a class="nav-link" href="index.php">首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-plus-circle text-primary"></i> 添加原材料</h2>
                <p class="text-muted mb-0">添加新的原材料到系统中</p>
            </div>
            <div>
                <a href="materials.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>

        <!-- 消息提示 -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- 添加表单 -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cube"></i> 原材料信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">
                                            <i class="fas fa-tag"></i> 原材料名称 <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="name" name="name" required 
                                               placeholder="例如：糯米粉" maxlength="100"
                                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                                        <div class="form-text">原材料的名称，必填项</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="specification" class="form-label">
                                            <i class="fas fa-info-circle"></i> 规格说明
                                        </label>
                                        <input type="text" class="form-control" id="specification" name="specification" 
                                               placeholder="例如：每袋500g" maxlength="100"
                                               value="<?php echo htmlspecialchars($_POST['specification'] ?? ''); ?>">
                                        <div class="form-text">规格描述，可选</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit" class="form-label">
                                            <i class="fas fa-balance-scale"></i> 单位 <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-control" id="unit" name="unit" required>
                                            <option value="">请选择单位</option>
                                            <?php 
                                            $units = ['袋', '包', '盒', '瓶', '罐', 'kg', 'g', 'L', 'ml', '个'];
                                            $selectedUnit = $_POST['unit'] ?? '';
                                            foreach ($units as $unit): 
                                            ?>
                                                <option value="<?php echo $unit; ?>" <?php echo $selectedUnit === $unit ? 'selected' : ''; ?>>
                                                    <?php echo $unit; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">计量单位，必填项</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="stock_quantity" class="form-label">
                                            <i class="fas fa-warehouse"></i> 初始库存
                                        </label>
                                        <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                               min="0" step="0.001" placeholder="0.000"
                                               value="<?php echo $_POST['stock_quantity'] ?? '0'; ?>">
                                        <div class="form-text">初始库存数量，默认为0</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unit_price" class="form-label">
                                            <i class="fas fa-money-bill"></i> 单价
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                                   min="0" step="0.01" placeholder="0.00"
                                                   value="<?php echo $_POST['unit_price'] ?? ''; ?>">
                                        </div>
                                        <div class="form-text">每单位的价格，用于成本计算，可选</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-calculator"></i> 库存价值
                                        </label>
                                        <div class="form-control-plaintext" id="stock_value">¥0.00</div>
                                        <div class="form-text">库存数量 × 单价</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 示例说明 -->
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> 填写示例：</h6>
                                <ul class="mb-0">
                                    <li><strong>糯米粉</strong> - 规格：每袋500g，单位：袋，单价：15.00元</li>
                                    <li><strong>淀粉</strong> - 规格：每袋250g，单位：袋，单价：8.50元</li>
                                    <li><strong>白糖</strong> - 规格：每袋1000g，单位：袋，单价：6.00元</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-end gap-2">
                                <a href="materials.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> 取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存原材料
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script>
    $(document).ready(function() {
        // 自动计算库存价值
        $('#stock_quantity, #unit_price').on('input', function() {
            var quantity = parseFloat($('#stock_quantity').val()) || 0;
            var price = parseFloat($('#unit_price').val()) || 0;
            var value = quantity * price;
            
            $('#stock_value').text('¥' + value.toFixed(2));
        });
        
        // 单位选择后的提示
        $('#unit').on('change', function() {
            var unit = $(this).val();
            if (unit) {
                $('#stock_quantity').attr('placeholder', '请输入数量（' + unit + '）');
            }
        });
    });
    </script>
</body>
</html>
