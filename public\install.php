<?php
/**
 * 数据库初始化脚本
 * 麻糍工厂销售系统
 */

// 引入数据库配置
require_once __DIR__ . '/../app/config/database.php';

$config = require __DIR__ . '/../app/config/database.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$config['host']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "<h2>麻糍工厂销售系统 - 数据库初始化</h2>";
    
    // 创建数据库（如果不存在）
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['dbname']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ 数据库 {$config['dbname']} 创建成功</p>";
    
    // 选择数据库
    $pdo->exec("USE `{$config['dbname']}`");
    
    // 创建用户表
    $sql = "
    CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password_hash` varchar(255) NOT NULL,
        `name` varchar(100) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 用户表创建成功</p>";
    
    // 创建原材料表
    $sql = "
    CREATE TABLE IF NOT EXISTS `materials` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `specification` varchar(100) DEFAULT NULL COMMENT '规格，如每袋500g',
        `unit` varchar(20) NOT NULL DEFAULT '袋' COMMENT '单位',
        `stock_quantity` decimal(10,3) NOT NULL DEFAULT 0 COMMENT '库存数量',
        `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_name` (`name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 原材料表创建成功</p>";
    
    // 创建成品表
    $sql = "
    CREATE TABLE IF NOT EXISTS `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `specification` varchar(100) DEFAULT NULL COMMENT '规格',
        `unit` varchar(20) NOT NULL DEFAULT '个' COMMENT '单位',
        `stock_quantity` decimal(10,3) NOT NULL DEFAULT 0 COMMENT '库存数量',
        `selling_price` decimal(10,2) NOT NULL COMMENT '销售单价',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_name` (`name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 成品表创建成功</p>";
    
    // 创建配方表
    $sql = "
    CREATE TABLE IF NOT EXISTS `recipes` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `name` varchar(100) DEFAULT NULL COMMENT '配方名称',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 配方表创建成功</p>";
    
    // 创建配方明细表
    $sql = "
    CREATE TABLE IF NOT EXISTS `recipe_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `recipe_id` int(11) NOT NULL,
        `material_id` int(11) NOT NULL,
        `quantity_needed` decimal(10,3) NOT NULL COMMENT '所需数量',
        `unit_of_material` varchar(20) NOT NULL COMMENT '原材料单位',
        PRIMARY KEY (`id`),
        KEY `idx_recipe_id` (`recipe_id`),
        KEY `idx_material_id` (`material_id`),
        FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`material_id`) REFERENCES `materials`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 配方明细表创建成功</p>";
    
    // 创建客户表
    $sql = "
    CREATE TABLE IF NOT EXISTS `customers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `contact_info` varchar(200) DEFAULT NULL COMMENT '联系方式',
        `balance` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '当前欠款',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_name` (`name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p>✓ 客户表创建成功</p>";
    
    echo "<h3>数据库初始化完成！</h3>";
    echo "<p><a href='create_admin.php'>点击这里创建管理员账号</a></p>";
    echo "<p><a href='index.php'>返回系统首页</a></p>";
    
} catch (PDOException $e) {
    die("数据库错误: " . $e->getMessage());
}
