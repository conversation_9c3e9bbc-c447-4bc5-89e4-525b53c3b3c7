<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-plus-circle text-primary"></i> 创建配方</h2>
        <p class="text-muted mb-0">为成品配置生产配方和原材料配比</p>
    </div>
    <div>
        <a href="index.php?controller=recipe&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 创建表单 -->
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-ul"></i> 配方信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=recipe&action=store">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product_id" class="form-label">
                                    <i class="fas fa-box"></i> 选择成品 <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="product_id" name="product_id" required>
                                    <option value="">请选择成品</option>
                                    <?php foreach ($products as $product): ?>
                                        <option value="<?php echo $product['id']; ?>" 
                                                <?php echo $selectedProductId == $product['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($product['name']); ?>
                                            (<?php echo htmlspecialchars($product['unit']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">选择要配置配方的成品</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipe_name" class="form-label">
                                    <i class="fas fa-tag"></i> 配方名称
                                </label>
                                <input type="text" class="form-control" id="recipe_name" name="recipe_name" 
                                       placeholder="例如：标准配方" maxlength="100">
                                <div class="form-text">配方的名称，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 原材料配置 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-cubes"></i> 原材料配比</h6>
                            <button type="button" class="btn btn-sm btn-success" id="add-material">
                                <i class="fas fa-plus"></i> 添加原材料
                            </button>
                        </div>
                        
                        <div id="materials-container">
                            <!-- 原材料行将通过JavaScript动态添加 -->
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle"></i> 配方说明：</h6>
                            <ul class="mb-0">
                                <li>配方定义了生产<strong>1个单位</strong>成品所需的原材料重量</li>
                                <li>所有用量统一使用<strong>克(g)</strong>为单位</li>
                                <li>例如：生产1个麻糍需要糯米粉50g、白糖25g</li>
                                <li>系统会根据配方自动计算生产成本和库存扣减</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=recipe&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存配方
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 原材料行模板 -->
<template id="material-row-template">
    <div class="material-row border rounded p-3 mb-3">
        <div class="row align-items-end">
            <div class="col-md-4">
                <label class="form-label">原材料</label>
                <div class="input-group">
                    <select class="form-control material-select" name="materials[][material_id]" required>
                        <option value="">请选择原材料</option>
                        <?php foreach ($materials as $material): ?>
                            <option value="<?php echo $material['id']; ?>"
                                    data-unit="<?php echo htmlspecialchars($material['unit']); ?>">
                                <?php echo htmlspecialchars($material['name']); ?>
                                <?php if ($material['specification']): ?>
                                    (<?php echo htmlspecialchars($material['specification']); ?>)
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" class="btn btn-outline-success quick-add-material-btn"
                            title="快捷添加原材料">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">所需重量(g)</label>
                <input type="number" class="form-control" name="materials[][quantity]"
                       min="0" step="0.1" placeholder="0.0" required>
            </div>
            <div class="col-md-3">
                <label class="form-label">单位</label>
                <input type="text" class="form-control unit-display" name="materials[][unit]"
                       readonly value="g" placeholder="克">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger btn-sm remove-material w-100">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    </div>
</template>

<script>
// 确保DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing recipe create page...');

    // 检查jQuery是否加载
    if (typeof $ === 'undefined') {
        console.error('jQuery not loaded!');
        return;
    }

    console.log('jQuery loaded, setting up event handlers...');

    // 添加原材料行
    $('#add-material').on('click', function() {
        console.log('Add material button clicked');
        var template = $('#material-row-template').html();
        if (!template) {
            console.error('Template not found!');
            return;
        }
        $('#materials-container').append(template);
        console.log('Material row added');
    });

    // 删除原材料行
    $(document).on('click', '.remove-material', function() {
        console.log('Remove material button clicked');
        $(this).closest('.material-row').remove();
    });

    // 原材料选择变化时（单位固定为g，无需更新）
    $(document).on('change', '.material-select', function() {
        // 单位固定为克(g)，无需更新
        $(this).closest('.material-row').find('.unit-display').val('g');
    });

    // 快捷添加原材料按钮点击
    $(document).on('click', '.quick-add-material-btn', function() {
        var $currentRow = $(this).closest('.material-row');
        window.currentMaterialRow = $currentRow; // 保存当前行引用
        $('#quickAddMaterialModal').modal('show');
    });

    // 初始添加一行
    setTimeout(function() {
        $('#add-material').trigger('click');
        console.log('Initial material row added');
    }, 100);

    // 表单验证
    $('form').on('submit', function(e) {
        var materialRows = $('.material-row').length;
        if (materialRows === 0) {
            e.preventDefault();
            alert('请至少添加一种原材料');
            return false;
        }

        var hasEmptyMaterial = false;
        $('.material-row').each(function() {
            var materialId = $(this).find('.material-select').val();
            var quantity = $(this).find('input[name*="quantity"]').val();

            if (!materialId || !quantity || parseFloat(quantity) <= 0) {
                hasEmptyMaterial = true;
                return false;
            }
        });

        if (hasEmptyMaterial) {
            e.preventDefault();
            alert('请完整填写所有原材料信息，且数量必须大于0');
            return false;
        }
    });
});

// 备用的jQuery ready方法
$(document).ready(function() {
    console.log('jQuery ready fired');

    // 如果按钮还没有绑定事件，重新绑定
    if (!$('#add-material').data('events')) {
        console.log('Re-binding add material button');
        $('#add-material').off('click').on('click', function() {
            console.log('Add material button clicked (backup handler)');
            var template = $('#material-row-template').html();
            if (template) {
                $('#materials-container').append(template);
            } else {
                console.error('Template not found in backup handler!');
            }
        });
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
