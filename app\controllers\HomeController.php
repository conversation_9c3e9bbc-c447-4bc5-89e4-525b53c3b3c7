<?php
/**
 * 首页控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';

class HomeController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->requireAuth(); // 需要登录
    }

    /**
     * 首页
     */
    public function index()
    {
        // 获取统计数据
        $stats = $this->getStats();
        
        // 获取最近的销售记录
        $recentSales = $this->getRecentSales();
        
        // 获取库存警告
        $lowStockItems = $this->getLowStockItems();
        
        $this->view('home/index', [
            'title' => '系统首页',
            'stats' => $stats,
            'recentSales' => $recentSales,
            'lowStockItems' => $lowStockItems
        ]);
    }

    /**
     * 获取统计数据
     */
    private function getStats()
    {
        $stats = [];
        
        try {
            // 成品总数
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM products");
            $stats['products_count'] = $result['count'] ?? 0;
            
            // 原材料总数
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM materials");
            $stats['materials_count'] = $result['count'] ?? 0;
            
            // 客户总数
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM customers");
            $stats['customers_count'] = $result['count'] ?? 0;
            
            // 今日销售额（如果有销售表的话）
            $stats['today_sales'] = 0; // 暂时设为0，后续实现销售功能后更新
            
            // 总欠款
            $result = $this->db->fetch("SELECT SUM(balance) as total FROM customers WHERE balance > 0");
            $stats['total_debt'] = $result['total'] ?? 0;
            
        } catch (Exception $e) {
            error_log("获取统计数据失败: " . $e->getMessage());
            $stats = [
                'products_count' => 0,
                'materials_count' => 0,
                'customers_count' => 0,
                'today_sales' => 0,
                'total_debt' => 0
            ];
        }
        
        return $stats;
    }

    /**
     * 获取最近的销售记录
     */
    private function getRecentSales()
    {
        // 暂时返回空数组，后续实现销售功能后更新
        return [];
    }

    /**
     * 获取库存不足的商品
     */
    private function getLowStockItems()
    {
        $lowStockItems = [];
        
        try {
            // 获取库存不足的成品（库存小于10）
            $products = $this->db->fetchAll(
                "SELECT name, stock_quantity, unit, 'product' as type FROM products WHERE stock_quantity < 10 ORDER BY stock_quantity ASC LIMIT 10"
            );
            
            // 获取库存不足的原材料（库存小于5）
            $materials = $this->db->fetchAll(
                "SELECT name, stock_quantity, unit, 'material' as type FROM materials WHERE stock_quantity < 5 ORDER BY stock_quantity ASC LIMIT 10"
            );
            
            $lowStockItems = array_merge($products, $materials);
            
        } catch (Exception $e) {
            error_log("获取库存警告失败: " . $e->getMessage());
        }
        
        return $lowStockItems;
    }
}
