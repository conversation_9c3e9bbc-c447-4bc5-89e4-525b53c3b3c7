<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-plus-circle text-primary"></i> 添加客户</h2>
        <p class="text-muted mb-0">添加新的客户到系统中</p>
    </div>
    <div>
        <a href="index.php?controller=customer&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 添加表单 -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> 客户信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=customer&action=store">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> 客户名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="例如：张三" maxlength="100">
                                <div class="form-text">客户的姓名或公司名称，必填项</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_info" class="form-label">
                                    <i class="fas fa-phone"></i> 联系方式
                                </label>
                                <input type="text" class="form-control" id="contact_info" name="contact_info" 
                                       placeholder="电话、微信、地址等" maxlength="200">
                                <div class="form-text">电话、微信、地址等联系信息，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="balance" class="form-label">
                                    <i class="fas fa-money-bill"></i> 初始余额
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="balance" name="balance" 
                                           step="0.01" value="0.00" placeholder="0.00">
                                </div>
                                <div class="form-text">
                                    正数表示客户欠款，负数表示预付款，默认为0
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-info-circle"></i> 余额说明
                                </label>
                                <div class="form-control-plaintext">
                                    <small class="text-muted">
                                        • 余额 > 0：客户欠款<br>
                                        • 余额 = 0：账目结清<br>
                                        • 余额 < 0：客户预付
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 示例说明 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> 填写示例：</h6>
                        <ul class="mb-0">
                            <li><strong>个人客户</strong> - 姓名：李四，联系方式：13800138000</li>
                            <li><strong>企业客户</strong> - 名称：某某餐厅，联系方式：老板微信xxx，地址：xxx街道</li>
                            <li><strong>老客户</strong> - 如果有历史欠款，可以在初始余额中填入</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=customer&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存客户
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 下一步提示 -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle text-info"></i> 下一步操作
                </h6>
                <p class="card-text">
                    客户创建后，您可以：
                </p>
                <ul>
                    <li>为客户开具销售单</li>
                    <li>查看客户的购买历史</li>
                    <li>调整客户的账目余额</li>
                    <li>管理客户的联系信息</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 余额输入格式化
    $('#balance').on('blur', function() {
        var value = parseFloat($(this).val()) || 0;
        $(this).val(value.toFixed(2));
    });
    
    // 联系方式输入提示
    $('#contact_info').on('focus', function() {
        if ($(this).val() === '') {
            $(this).attr('placeholder', '例如：13800138000 或 微信：abc123 或 地址：某某街道123号');
        }
    }).on('blur', function() {
        $(this).attr('placeholder', '电话、微信、地址等');
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
