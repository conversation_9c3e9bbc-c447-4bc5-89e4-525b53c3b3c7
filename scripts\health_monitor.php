<?php
/**
 * 系统健康监控定时任务
 * 可以通过cron定时执行
 * 
 * 使用方法:
 * php scripts/health_monitor.php
 * 
 * Cron示例 (每5分钟检查一次):
 * */5 * * * * /usr/bin/php /path/to/your/project/scripts/health_monitor.php
 */

// 设置脚本路径
$scriptDir = dirname(__FILE__);
$projectRoot = dirname($scriptDir);

// 引入必要文件
require_once $projectRoot . '/app/core/SystemHealthChecker.php';

// 配置
$config = [
    'log_file' => $projectRoot . '/logs/health_monitor.log',
    'alert_webhook' => null, // 可以设置webhook URL用于告警
    'alert_email' => null,   // 可以设置邮箱用于告警
    'check_interval' => 300, // 检查间隔（秒）
];

// 创建日志目录
$logDir = dirname($config['log_file']);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 记录日志函数
function writeLog($message, $level = 'INFO') {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents($config['log_file'], $logEntry, FILE_APPEND | LOCK_EX);
}

// 发送告警函数
function sendAlert($title, $message, $level = 'WARNING') {
    global $config;
    
    writeLog("发送告警: $title - $message", $level);
    
    // 如果配置了webhook，发送到webhook
    if ($config['alert_webhook']) {
        $payload = json_encode([
            'title' => $title,
            'message' => $message,
            'level' => $level,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $ch = curl_init($config['alert_webhook']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_exec($ch);
        curl_close($ch);
    }
    
    // 如果配置了邮箱，发送邮件（需要配置邮件服务）
    if ($config['alert_email']) {
        // 这里可以集成邮件发送功能
        // mail($config['alert_email'], $title, $message);
    }
}

// 主执行逻辑
try {
    writeLog("开始系统健康检查");
    
    // 创建健康检查器
    $healthChecker = new SystemHealthChecker();
    
    // 执行检查
    $report = $healthChecker->runFullCheck();
    
    // 分析结果
    $summary = $report['summary'];
    $overallStatus = $report['overall_status'];
    
    writeLog("检查完成 - 总体状态: $overallStatus, 通过: {$summary['passed']}, 警告: {$summary['warnings']}, 失败: {$summary['failed']}");
    
    // 处理失败项目
    if ($summary['failed'] > 0) {
        $failedItems = [];
        foreach ($report['details'] as $category => $checks) {
            foreach ($checks as $checkName => $check) {
                if ($check['status'] === 'fail') {
                    $failedItems[] = "{$check['name']}: {$check['message']}";
                }
            }
        }
        
        $alertMessage = "系统检查发现 {$summary['failed']} 个严重问题:\n" . implode("\n", $failedItems);
        sendAlert("系统健康检查 - 严重问题", $alertMessage, 'CRITICAL');
    }
    
    // 处理警告项目
    if ($summary['warnings'] > 0) {
        $warningItems = [];
        foreach ($report['details'] as $category => $checks) {
            foreach ($checks as $checkName => $check) {
                if ($check['status'] === 'warning') {
                    $warningItems[] = "{$check['name']}: {$check['message']}";
                }
            }
        }
        
        $alertMessage = "系统检查发现 {$summary['warnings']} 个警告项目:\n" . implode("\n", $warningItems);
        sendAlert("系统健康检查 - 警告", $alertMessage, 'WARNING');
    }
    
    // 保存检查结果到文件
    $resultFile = $projectRoot . '/logs/latest_health_report.json';
    file_put_contents($resultFile, json_encode($report, JSON_PRETTY_PRINT));
    
    writeLog("健康检查完成，结果已保存");
    
    // 输出结果（用于命令行查看）
    echo "系统健康检查完成\n";
    echo "总体状态: $overallStatus\n";
    echo "检查项目: {$summary['total_checks']}\n";
    echo "通过: {$summary['passed']}\n";
    echo "警告: {$summary['warnings']}\n";
    echo "失败: {$summary['failed']}\n";
    echo "执行时间: " . number_format($summary['execution_time'], 2) . "ms\n";
    
    if ($summary['failed'] > 0) {
        echo "\n严重问题:\n";
        foreach ($report['details'] as $category => $checks) {
            foreach ($checks as $checkName => $check) {
                if ($check['status'] === 'fail') {
                    echo "- {$check['name']}: {$check['message']}\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    $errorMessage = "健康检查执行失败: " . $e->getMessage();
    writeLog($errorMessage, 'ERROR');
    sendAlert("系统健康检查 - 执行失败", $errorMessage, 'CRITICAL');
    
    echo "错误: $errorMessage\n";
    exit(1);
}

// 清理旧日志文件（保留最近30天）
$logFiles = glob(dirname($config['log_file']) . '/*.log');
foreach ($logFiles as $logFile) {
    if (filemtime($logFile) < time() - (30 * 24 * 60 * 60)) {
        unlink($logFile);
    }
}

exit(0);
?>
