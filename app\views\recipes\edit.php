<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-edit text-primary"></i> 编辑配方</h2>
        <p class="text-muted mb-0">修改配方的原材料配比</p>
    </div>
    <div>
        <a href="index.php?controller=recipe&action=index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 编辑表单 -->
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-ul"></i> 编辑配方信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?controller=recipe&action=update">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="recipe_id" value="<?php echo $recipe['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-box"></i> 成品
                                </label>
                                <div class="form-control-plaintext">
                                    <strong><?php echo htmlspecialchars($recipe['product_name'] ?? '未知成品'); ?></strong>
                                </div>
                                <div class="form-text">成品信息不可修改</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipe_name" class="form-label">
                                    <i class="fas fa-tag"></i> 配方名称
                                </label>
                                <input type="text" class="form-control" id="recipe_name" name="recipe_name" 
                                       placeholder="例如：标准配方" maxlength="100"
                                       value="<?php echo htmlspecialchars($recipe['name'] ?? ''); ?>">
                                <div class="form-text">配方的名称，可选</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 原材料配置 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-cubes"></i> 原材料配比</h6>
                            <button type="button" class="btn btn-sm btn-success" id="add-material">
                                <i class="fas fa-plus"></i> 添加原材料
                            </button>
                        </div>
                        
                        <div id="materials-container">
                            <!-- 现有原材料 -->
                            <?php if (!empty($recipe['items'])): ?>
                                <?php foreach ($recipe['items'] as $item): ?>
                                    <div class="material-row border rounded p-3 mb-3">
                                        <div class="row align-items-end">
                                            <div class="col-md-4">
                                                <label class="form-label">原材料</label>
                                                <select class="form-control material-select" name="materials[][material_id]" required>
                                                    <option value="">请选择原材料</option>
                                                    <?php foreach ($materials as $material): ?>
                                                        <option value="<?php echo $material['id']; ?>" 
                                                                data-unit="<?php echo htmlspecialchars($material['unit']); ?>"
                                                                <?php echo $material['id'] == $item['material_id'] ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($material['name']); ?>
                                                            <?php if ($material['specification']): ?>
                                                                (<?php echo htmlspecialchars($material['specification']); ?>)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">所需重量(g)</label>
                                                <input type="number" class="form-control" name="materials[][quantity]"
                                                       min="0" step="0.1" required
                                                       value="<?php echo $item['quantity_needed']; ?>">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">单位</label>
                                                <input type="text" class="form-control unit-display" name="materials[][unit]"
                                                       readonly value="g">
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-outline-danger btn-sm remove-material w-100">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle"></i> 配方说明：</h6>
                            <ul class="mb-0">
                                <li>配方定义了生产<strong>1个单位</strong>成品所需的原材料重量</li>
                                <li>所有用量统一使用<strong>克(g)</strong>为单位</li>
                                <li>修改配方会影响后续的生产成本计算</li>
                                <li>建议在修改前备份重要数据</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 配方历史信息 -->
                    <div class="alert alert-light">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            创建时间：<?php echo date('Y-m-d H:i:s', strtotime($recipe['created_at'])); ?>
                            <?php if ($recipe['updated_at'] !== $recipe['created_at']): ?>
                                <br>
                                <i class="fas fa-edit"></i> 
                                最后更新：<?php echo date('Y-m-d H:i:s', strtotime($recipe['updated_at'])); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="index.php?controller=recipe&action=index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 原材料行模板 -->
<template id="material-row-template">
    <div class="material-row border rounded p-3 mb-3">
        <div class="row align-items-end">
            <div class="col-md-4">
                <label class="form-label">原材料</label>
                <select class="form-control material-select" name="materials[][material_id]" required>
                    <option value="">请选择原材料</option>
                    <?php foreach ($materials as $material): ?>
                        <option value="<?php echo $material['id']; ?>" 
                                data-unit="<?php echo htmlspecialchars($material['unit']); ?>">
                            <?php echo htmlspecialchars($material['name']); ?>
                            <?php if ($material['specification']): ?>
                                (<?php echo htmlspecialchars($material['specification']); ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">所需重量(g)</label>
                <input type="number" class="form-control" name="materials[][quantity]"
                       min="0" step="0.1" placeholder="0.0" required>
            </div>
            <div class="col-md-3">
                <label class="form-label">单位</label>
                <input type="text" class="form-control unit-display" name="materials[][unit]"
                       readonly value="g" placeholder="克">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger btn-sm remove-material w-100">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    </div>
</template>

<script>
// 确保DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing recipe edit page...');

    // 检查jQuery是否加载
    if (typeof $ === 'undefined') {
        console.error('jQuery not loaded!');
        return;
    }

    console.log('jQuery loaded, setting up event handlers...');

    // 添加原材料行
    $('#add-material').on('click', function() {
        console.log('Add material button clicked');
        var template = $('#material-row-template').html();
        if (!template) {
            console.error('Template not found!');
            return;
        }
        $('#materials-container').append(template);
        console.log('Material row added');
    });

    // 删除原材料行
    $(document).on('click', '.remove-material', function() {
        console.log('Remove material button clicked');
        $(this).closest('.material-row').remove();
    });

    // 原材料选择变化时（单位固定为g，无需更新）
    $(document).on('change', '.material-select', function() {
        // 单位固定为克(g)，无需更新
        $(this).closest('.material-row').find('.unit-display').val('g');
    });

    // 表单验证
    $('form').on('submit', function(e) {
        var materialRows = $('.material-row').length;
        if (materialRows === 0) {
            e.preventDefault();
            alert('请至少添加一种原材料');
            return false;
        }

        var hasEmptyMaterial = false;
        $('.material-row').each(function() {
            var materialId = $(this).find('.material-select').val();
            var quantity = $(this).find('input[name*="quantity"]').val();

            if (!materialId || !quantity || parseFloat(quantity) <= 0) {
                hasEmptyMaterial = true;
                return false;
            }
        });

        if (hasEmptyMaterial) {
            e.preventDefault();
            alert('请完整填写所有原材料信息，且数量必须大于0');
            return false;
        }
    });
});

// 备用的jQuery ready方法
$(document).ready(function() {
    console.log('jQuery ready fired for edit page');

    // 如果按钮还没有绑定事件，重新绑定
    if (!$('#add-material').data('events')) {
        console.log('Re-binding add material button for edit page');
        $('#add-material').off('click').on('click', function() {
            console.log('Add material button clicked (backup handler)');
            var template = $('#material-row-template').html();
            if (template) {
                $('#materials-container').append(template);
            } else {
                console.error('Template not found in backup handler!');
            }
        });
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
