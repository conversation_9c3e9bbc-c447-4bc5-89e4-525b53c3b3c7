<?php
/**
 * 客户模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class Customer
{
    private $db;
    private $table = 'customers';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取所有客户
     */
    public function getAll($orderBy = 'name', $order = 'ASC')
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY {$orderBy} {$order}";
        return $this->db->fetchAll($sql);
    }

    /**
     * 根据ID获取客户
     */
    public function getById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? LIMIT 1";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 创建新客户
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (name, contact_info, balance, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())";

        $params = [
            $data['name'],
            $data['contact_info'] ?? null,
            $data['balance'] ?? 0
        ];

        $this->db->execute($sql, $params);
        return $this->db->lastInsertId();
    }

    /**
     * 更新客户
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [];
        
        if (isset($data['name'])) {
            $fields[] = 'name = ?';
            $params[] = $data['name'];
        }
        
        if (isset($data['contact_info'])) {
            $fields[] = 'contact_info = ?';
            $params[] = $data['contact_info'];
        }
        
        if (isset($data['balance'])) {
            $fields[] = 'balance = ?';
            $params[] = $data['balance'];
        }
        
        if (empty($fields)) {
            return false;
        }

        // 添加更新时间
        $fields[] = 'updated_at = NOW()';
        $params[] = $id;
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";

        return $this->db->execute($sql, $params) > 0;
    }

    /**
     * 删除客户
     */
    public function delete($id)
    {
        // 检查是否有销售记录（后续实现销售功能时添加）
        // $hasSales = $this->db->fetch("SELECT COUNT(*) as count FROM sales WHERE customer_id = ?", [$id]);
        // if ($hasSales['count'] > 0) {
        //     throw new Exception('该客户有销售记录，无法删除');
        // }
        
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->execute($sql, [$id]) > 0;
    }

    /**
     * 检查名称是否已存在
     */
    public function nameExists($name, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }

    /**
     * 更新客户余额
     */
    public function updateBalance($id, $amount, $operation = 'set')
    {
        if ($operation === 'add') {
            $sql = "UPDATE {$this->table} SET balance = balance + ? WHERE id = ?";
        } elseif ($operation === 'subtract') {
            $sql = "UPDATE {$this->table} SET balance = balance - ? WHERE id = ?";
        } else {
            $sql = "UPDATE {$this->table} SET balance = ? WHERE id = ?";
        }
        
        return $this->db->execute($sql, [$amount, $id]) > 0;
    }

    /**
     * 获取有欠款的客户
     */
    public function getWithDebt()
    {
        $sql = "SELECT * FROM {$this->table} WHERE balance > 0 ORDER BY balance DESC";
        return $this->db->fetchAll($sql);
    }

    /**
     * 搜索客户
     */
    public function search($keyword)
    {
        $sql = "SELECT * FROM {$this->table} WHERE name LIKE ? OR contact_info LIKE ? ORDER BY name";
        $searchTerm = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }

    /**
     * 获取统计信息
     */
    public function getStats()
    {
        $stats = [];
        
        // 总客户数
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total_count'] = $result['count'];
        
        // 有欠款的客户数
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table} WHERE balance > 0");
        $stats['debt_customers_count'] = $result['count'];
        
        // 总欠款金额
        $result = $this->db->fetch("SELECT SUM(balance) as total_debt FROM {$this->table} WHERE balance > 0");
        $stats['total_debt'] = $result['total_debt'] ?? 0;
        
        // 平均欠款
        if ($stats['debt_customers_count'] > 0) {
            $stats['average_debt'] = $stats['total_debt'] / $stats['debt_customers_count'];
        } else {
            $stats['average_debt'] = 0;
        }
        
        return $stats;
    }

    /**
     * 获取客户的销售历史（后续实现）
     */
    public function getSalesHistory($customerId, $limit = 10)
    {
        // 暂时返回空数组，后续实现销售功能时更新
        return [];
    }

    /**
     * 记录客户账目变动（后续实现）
     */
    public function recordTransaction($customerId, $amount, $type, $description = '')
    {
        // 后续实现账目记录功能
        return true;
    }
}
