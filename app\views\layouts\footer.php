    </main>
    
    <?php if (isset($_SESSION['user_id'])): ?>
        <!-- 页脚 -->
        <footer class="bg-light mt-5 py-4">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            &copy; <?php echo date('Y'); ?> 麻糍工厂销售系统. 版权所有.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-muted mb-0">
                            当前用户: <?php echo htmlspecialchars($_SESSION['name'] ?? $_SESSION['username']); ?>
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    <?php endif; ?>
    
    <!-- jQuery -->
    <script src="assets/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="assets/js/bootstrap.min.js"></script>
    <!-- 自定义JS -->
    <script src="assets/js/app.js"></script>
    
    <!-- Font Awesome (图标库) -->
    <script src="https://kit.fontawesome.com/your-kit-id.js" crossorigin="anonymous"></script>

    <!-- 快捷添加原材料模态框 -->
    <div class="modal fade" id="quickAddMaterialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle text-primary"></i> 快捷添加原材料
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quickAddMaterialForm">
                        <div class="mb-3">
                            <label for="quick_material_name" class="form-label">
                                原材料名称 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="quick_material_name"
                                   placeholder="例如：糯米粉" maxlength="100" required>
                        </div>
                        <div class="mb-3">
                            <label for="quick_material_specification" class="form-label">规格说明</label>
                            <input type="text" class="form-control" id="quick_material_specification"
                                   placeholder="例如：每袋500g" maxlength="100">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quick_material_unit" class="form-label">
                                        单位 <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="quick_material_unit" required>
                                        <option value="">请选择单位</option>
                                        <option value="袋">袋</option>
                                        <option value="包">包</option>
                                        <option value="盒">盒</option>
                                        <option value="瓶">瓶</option>
                                        <option value="罐">罐</option>
                                        <option value="kg">kg</option>
                                        <option value="g">g</option>
                                        <option value="L">L</option>
                                        <option value="ml">ml</option>
                                        <option value="个">个</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quick_material_price" class="form-label">单价</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="quick_material_price"
                                               min="0" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                添加后会自动选中该原材料，初始库存为0
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveQuickMaterial">
                        <i class="fas fa-save"></i> 保存并选中
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷添加客户模态框 -->
    <div class="modal fade" id="quickAddCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus text-primary"></i> 快捷添加客户
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quickAddCustomerForm">
                        <div class="mb-3">
                            <label for="quick_customer_name" class="form-label">
                                客户名称 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="quick_customer_name"
                                   placeholder="例如：张三" maxlength="100" required>
                        </div>
                        <div class="mb-3">
                            <label for="quick_customer_contact" class="form-label">联系方式</label>
                            <input type="text" class="form-control" id="quick_customer_contact"
                                   placeholder="电话、微信、地址等" maxlength="200">
                        </div>
                        <div class="mb-3">
                            <label for="quick_customer_balance" class="form-label">初始余额</label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control" id="quick_customer_balance"
                                       step="0.01" value="0.00" placeholder="0.00">
                            </div>
                            <div class="form-text">正数表示客户欠款，负数表示预付款</div>
                        </div>
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                添加后会自动选中该客户
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveQuickCustomer">
                        <i class="fas fa-save"></i> 保存并选中
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷添加原材料的JavaScript -->
    <script>
    $(document).ready(function() {
        // 保存快捷添加原材料
        $('#saveQuickMaterial').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();

            // 获取表单数据
            var formData = {
                name: $('#quick_material_name').val().trim(),
                specification: $('#quick_material_specification').val().trim(),
                unit: $('#quick_material_unit').val(),
                unit_price: $('#quick_material_price').val()
            };

            // 验证
            if (!formData.name) {
                alert('请输入原材料名称');
                return;
            }

            if (!formData.unit) {
                alert('请选择单位');
                return;
            }

            // 显示加载状态
            $btn.html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
            $btn.prop('disabled', true);

            // 发送AJAX请求
            $.ajax({
                url: 'index.php?controller=material&action=quickAdd',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // 添加到所有原材料选择框
                        var newOption = '<option value="' + response.material.id + '" data-unit="' + response.material.unit + '">' +
                                       response.material.name;
                        if (response.material.specification) {
                            newOption += ' (' + response.material.specification + ')';
                        }
                        newOption += '</option>';

                        $('.material-select').each(function() {
                            $(this).append(newOption);
                        });

                        // 如果有当前行，自动选中新添加的原材料
                        if (window.currentMaterialRow) {
                            window.currentMaterialRow.find('.material-select').val(response.material.id);
                            window.currentMaterialRow.find('.unit-display').val('g');
                        }

                        // 关闭模态框
                        $('#quickAddMaterialModal').modal('hide');

                        // 清空表单
                        $('#quickAddMaterialForm')[0].reset();

                        // 显示成功消息
                        if (typeof toastr !== 'undefined') {
                            toastr.success(response.message);
                        } else {
                            alert(response.message);
                        }
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('网络错误，请重试');
                },
                complete: function() {
                    // 恢复按钮状态
                    $btn.html(originalText);
                    $btn.prop('disabled', false);
                }
            });
        });

        // 模态框关闭时清空表单
        $('#quickAddMaterialModal').on('hidden.bs.modal', function() {
            $('#quickAddMaterialForm')[0].reset();
            window.currentMaterialRow = null;
        });

        // 保存快捷添加客户
        $('#saveQuickCustomer').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();

            // 获取表单数据
            var formData = {
                name: $('#quick_customer_name').val().trim(),
                contact_info: $('#quick_customer_contact').val().trim(),
                balance: $('#quick_customer_balance').val()
            };

            // 验证
            if (!formData.name) {
                alert('请输入客户名称');
                return;
            }

            // 显示加载状态
            $btn.html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
            $btn.prop('disabled', true);

            // 发送AJAX请求
            $.ajax({
                url: 'index.php?controller=customer&action=quickAdd',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // 添加到客户选择框
                        var newOption = '<option value="' + response.customer.id + '" data-balance="' + response.customer.balance + '">' +
                                       response.customer.name;
                        if (response.customer.balance > 0) {
                            newOption += ' (欠款: ¥' + parseFloat(response.customer.balance).toFixed(2) + ')';
                        }
                        newOption += '</option>';

                        $('#customer_id').append(newOption);

                        // 自动选中新添加的客户
                        $('#customer_id').val(response.customer.id).trigger('change');

                        // 关闭模态框
                        $('#quickAddCustomerModal').modal('hide');

                        // 清空表单
                        $('#quickAddCustomerForm')[0].reset();

                        // 显示成功消息
                        if (typeof toastr !== 'undefined') {
                            toastr.success(response.message);
                        } else {
                            alert(response.message);
                        }
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('网络错误，请重试');
                },
                complete: function() {
                    // 恢复按钮状态
                    $btn.html(originalText);
                    $btn.prop('disabled', false);
                }
            });
        });

        // 客户模态框关闭时清空表单
        $('#quickAddCustomerModal').on('hidden.bs.modal', function() {
            $('#quickAddCustomerForm')[0].reset();
        });
    });
    </script>

    <!-- Flash消息显示 -->
    <?php if (isset($_SESSION['flash'])): ?>
        <script>
            $(document).ready(function() {
                <?php foreach ($_SESSION['flash'] as $type => $message): ?>
                    showAlert('<?php echo $type; ?>', '<?php echo addslashes($message); ?>');
                    <?php unset($_SESSION['flash'][$type]); ?>
                <?php endforeach; ?>
            });
        </script>
    <?php endif; ?>
</body>
</html>
