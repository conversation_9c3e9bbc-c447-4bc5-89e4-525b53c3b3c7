<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-warehouse"></i> <?php echo $title; ?></h2>
    <div class="btn-group">
        <a href="index.php?controller=inventory&action=adjust" class="btn btn-primary">
            <i class="fas fa-edit"></i> 库存调整
        </a>
        <a href="index.php?controller=inventory&action=logs" class="btn btn-info">
            <i class="fas fa-history"></i> 变动记录
        </a>
        <a href="index.php?controller=inventory&action=reports" class="btn btn-success">
            <i class="fas fa-chart-bar"></i> 库存报表
        </a>
    </div>
</div>

<!-- 库存统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">原材料种类</h6>
                        <h3 class="mb-0"><?php echo $materialStats['total_count']; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cubes fa-2x"></i>
                    </div>
                </div>
                <small>库存价值: ¥<?php echo number_format($materialStats['total_value'], 2); ?></small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">成品种类</h6>
                        <h3 class="mb-0"><?php echo $productStats['total_count']; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
                <small>库存价值: ¥<?php echo number_format($productStats['total_value'], 2); ?></small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">低库存原材料</h6>
                        <h3 class="mb-0"><?php echo count($lowStockMaterials); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
                <small>需要补货</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">低库存成品</h6>
                        <h3 class="mb-0"><?php echo count($lowStockProducts); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
                <small>需要生产</small>
            </div>
        </div>
    </div>
</div>

<!-- 低库存预警 -->
<?php if (!empty($lowStockMaterials) || !empty($lowStockProducts)): ?>
<div class="row mb-4">
    <?php if (!empty($lowStockMaterials)): ?>
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> 低库存原材料预警</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>原材料</th>
                                <th>当前库存</th>
                                <th>单位</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($lowStockMaterials as $material): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($material['name']); ?></td>
                                <td>
                                    <span class="badge bg-warning">
                                        <?php echo number_format($material['stock_quantity'], 2); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($material['unit']); ?></td>
                                <td>
                                    <a href="index.php?controller=inventory&action=adjust&type=material&id=<?php echo $material['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> 调整
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($lowStockProducts)): ?>
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0"><i class="fas fa-exclamation-circle"></i> 低库存成品预警</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>成品</th>
                                <th>当前库存</th>
                                <th>单位</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($lowStockProducts as $product): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($product['name']); ?></td>
                                <td>
                                    <span class="badge bg-danger">
                                        <?php echo number_format($product['stock_quantity'], 3); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($product['unit']); ?></td>
                                <td>
                                    <a href="index.php?controller=inventory&action=adjust&type=product&id=<?php echo $product['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> 调整
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- 最近库存变动 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fas fa-history"></i> 最近库存变动</h6>
            <a href="index.php?controller=inventory&action=logs" class="btn btn-sm btn-outline-info">
                查看全部
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($recentLogs)): ?>
            <p class="text-muted text-center">暂无库存变动记录</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>物品</th>
                            <th>类型</th>
                            <th>变动数量</th>
                            <th>变动后库存</th>
                            <th>操作员</th>
                            <th>原因</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentLogs as $log): ?>
                        <tr>
                            <td><?php echo date('m-d H:i', strtotime($log['created_at'])); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $log['item_type'] === 'material' ? 'secondary' : 'primary'; ?>">
                                    <?php echo $log['item_type'] === 'material' ? '原料' : '成品'; ?>
                                </span>
                                <?php echo htmlspecialchars($log['item_name']); ?>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo \InventoryLog::getChangeTypeText($log['change_type']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="<?php echo $log['quantity_change'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo $log['quantity_change'] > 0 ? '+' : ''; ?><?php echo number_format($log['quantity_change'], 3); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($log['quantity_after'], 3); ?></td>
                            <td><?php echo htmlspecialchars($log['operator_name']); ?></td>
                            <td><?php echo htmlspecialchars($log['reason']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 快速操作提示 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning"></i> 库存管理提示
                </h6>
                <ul class="mb-0">
                    <li>定期检查低库存预警，及时补货</li>
                    <li>库存调整会自动记录变动日志</li>
                    <li>建议设置合理的库存预警阈值</li>
                    <li>定期进行库存盘点，确保数据准确</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-tools text-primary"></i> 快速操作
                </h6>
                <div class="d-grid gap-2">
                    <a href="index.php?controller=material&action=index" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-cubes"></i> 管理原材料
                    </a>
                    <a href="index.php?controller=product&action=index" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-box"></i> 管理成品
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
