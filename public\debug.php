<?php
/**
 * 统一调试页面
 * 麻糍工厂销售系统
 */

session_start();

// 检查登录
if (!isset($_SESSION['user_id'])) {
    die("请先登录");
}

require_once '../app/core/Database.php';

$db = Database::getInstance();
$action = $_GET['action'] ?? 'overview';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统调试 - 麻糍工厂销售系统</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        .debug-table th, .debug-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .debug-table th { background-color: #f2f2f2; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-success { color: green; font-weight: bold; }
        .debug-error { color: red; font-weight: bold; }
        .debug-warning { color: orange; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-bug"></i> 系统调试面板</h1>
        
        <!-- 导航菜单 -->
        <div class="mb-4">
            <a href="debug.php?action=overview" class="btn btn-outline-primary <?php echo $action === 'overview' ? 'active' : ''; ?>">系统概览</a>
            <a href="debug.php?action=recipes" class="btn btn-outline-info <?php echo $action === 'recipes' ? 'active' : ''; ?>">配方调试</a>
            <a href="debug.php?action=database" class="btn btn-outline-secondary <?php echo $action === 'database' ? 'active' : ''; ?>">数据库状态</a>
            <a href="debug.php?action=logs" class="btn btn-outline-warning <?php echo $action === 'logs' ? 'active' : ''; ?>">错误日志</a>
            <a href="index.php" class="btn btn-success">返回首页</a>
        </div>

        <?php if ($action === 'overview'): ?>
            <!-- 系统概览 -->
            <div class="debug-section">
                <h3>系统概览</h3>
                <?php
                $stats = [];
                try {
                    $stats['原材料数量'] = $db->fetch("SELECT COUNT(*) as count FROM materials")['count'];
                } catch (Exception $e) {
                    $stats['原材料数量'] = '表不存在';
                }

                try {
                    $stats['成品数量'] = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
                } catch (Exception $e) {
                    $stats['成品数量'] = '表不存在';
                }

                try {
                    $stats['客户数量'] = $db->fetch("SELECT COUNT(*) as count FROM customers")['count'];
                } catch (Exception $e) {
                    $stats['客户数量'] = '表不存在';
                }

                try {
                    $stats['配方数量'] = $db->fetch("SELECT COUNT(*) as count FROM recipes")['count'];
                } catch (Exception $e) {
                    $stats['配方数量'] = '表不存在';
                }

                try {
                    $stats['销售记录数量'] = $db->fetch("SELECT COUNT(*) as count FROM sales")['count'];
                } catch (Exception $e) {
                    $stats['销售记录数量'] = '表不存在';
                }
                ?>
                <table class="debug-table">
                    <tr><th>项目</th><th>数量</th></tr>
                    <?php foreach ($stats as $item => $count): ?>
                        <tr><td><?php echo $item; ?></td><td><?php echo $count; ?></td></tr>
                    <?php endforeach; ?>
                </table>
            </div>

        <?php elseif ($action === 'recipes'): ?>
            <!-- 配方调试 -->
            <div class="debug-section">
                <h3>配方数据调试</h3>
                
                <h4>1. 配方表 (recipes)</h4>
                <?php
                $recipes = $db->fetchAll("SELECT * FROM recipes ORDER BY id");
                if (empty($recipes)): ?>
                    <p class="debug-error">❌ 配方表为空</p>
                <?php else: ?>
                    <p class="debug-success">✅ 找到 <?php echo count($recipes); ?> 个配方</p>
                    <table class="debug-table">
                        <tr><th>ID</th><th>Product ID</th><th>Name</th><th>Created At</th></tr>
                        <?php foreach ($recipes as $recipe): ?>
                            <tr>
                                <td><?php echo $recipe['id']; ?></td>
                                <td><?php echo $recipe['product_id']; ?></td>
                                <td><?php echo $recipe['name'] ?: '默认配方'; ?></td>
                                <td><?php echo $recipe['created_at']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>

                <h4>2. 配方项目表 (recipe_items)</h4>
                <?php
                $recipeItems = $db->fetchAll("SELECT ri.*, m.name as material_name FROM recipe_items ri LEFT JOIN materials m ON ri.material_id = m.id ORDER BY ri.recipe_id, ri.id");
                if (empty($recipeItems)): ?>
                    <p class="debug-error">❌ 配方项目表为空</p>
                <?php else: ?>
                    <p class="debug-success">✅ 找到 <?php echo count($recipeItems); ?> 个配方项目</p>
                    <table class="debug-table">
                        <tr><th>ID</th><th>Recipe ID</th><th>Material</th><th>Quantity</th><th>Unit</th></tr>
                        <?php foreach ($recipeItems as $item): ?>
                            <tr>
                                <td><?php echo $item['id']; ?></td>
                                <td><?php echo $item['recipe_id']; ?></td>
                                <td><?php echo $item['material_name'] ?: '未知原材料'; ?></td>
                                <td><?php echo $item['quantity_needed']; ?></td>
                                <td><?php echo $item['unit_of_material']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>

                <h4>3. 配方统计查询测试</h4>
                <?php
                $recipeStats = $db->fetchAll("
                    SELECT r.*, p.name as product_name, p.unit as product_unit,
                           (SELECT COUNT(*) FROM recipe_items ri WHERE ri.recipe_id = r.id) as material_count
                    FROM recipes r
                    JOIN products p ON r.product_id = p.id
                    ORDER BY p.name
                ");
                if (empty($recipeStats)): ?>
                    <p class="debug-error">❌ 没有配方统计数据</p>
                <?php else: ?>
                    <p class="debug-success">✅ 配方统计查询正常</p>
                    <table class="debug-table">
                        <tr><th>Recipe ID</th><th>Product</th><th>Recipe Name</th><th>Material Count</th></tr>
                        <?php foreach ($recipeStats as $stat): ?>
                            <tr>
                                <td><?php echo $stat['id']; ?></td>
                                <td><?php echo $stat['product_name']; ?></td>
                                <td><?php echo $stat['name'] ?: '默认配方'; ?></td>
                                <td><?php echo $stat['material_count']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
            </div>

        <?php elseif ($action === 'database'): ?>
            <!-- 数据库状态 -->
            <div class="debug-section">
                <h3>数据库表状态</h3>
                <?php
                $tables = ['materials', 'products', 'customers', 'recipes', 'recipe_items', 'sales', 'sale_items'];
                foreach ($tables as $table):
                    try {
                        $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
                        $structure = $db->fetchAll("DESCRIBE $table");
                        echo "<h4>表: $table (记录数: $count)</h4>";
                        echo "<table class='debug-table'>";
                        echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
                        foreach ($structure as $field) {
                            echo "<tr>";
                            echo "<td>{$field['Field']}</td>";
                            echo "<td>{$field['Type']}</td>";
                            echo "<td>{$field['Null']}</td>";
                            echo "<td>{$field['Key']}</td>";
                            echo "<td>{$field['Default']}</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    } catch (Exception $e) {
                        echo "<p class='debug-error'>❌ 表 $table 错误: " . $e->getMessage() . "</p>";
                    }
                endforeach;
                ?>
            </div>

        <?php elseif ($action === 'logs'): ?>
            <!-- 错误日志 -->
            <div class="debug-section">
                <h3>PHP错误日志</h3>
                <?php
                $logFiles = [
                    'D:\phpstudy_pro\Extensions\php\php7.3.4nts\logs\php_errors.log',
                    'D:\phpstudy_pro\Extensions\Nginx1.15.11\logs\error.log'
                ];
                
                foreach ($logFiles as $logFile):
                    if (file_exists($logFile)):
                        echo "<h4>日志文件: " . basename($logFile) . "</h4>";
                        $lines = file($logFile);
                        $recentLines = array_slice($lines, -20); // 最后20行
                        echo "<pre style='background: #f8f9fa; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
                        echo htmlspecialchars(implode('', $recentLines));
                        echo "</pre>";
                    else:
                        echo "<p class='debug-warning'>⚠️ 日志文件不存在: $logFile</p>";
                    endif;
                endforeach;
                ?>
            </div>
        <?php endif; ?>

        <!-- 快速操作 -->
        <div class="debug-section">
            <h3>快速操作</h3>
            <a href="init_database.php" class="btn btn-danger">初始化数据库</a>
            <a href="index.php?controller=recipe&action=create" class="btn btn-primary">创建配方</a>
            <a href="index.php?controller=recipe&action=index" class="btn btn-info">配方管理</a>
            <a href="debug_recipe_form.php" class="btn btn-warning">表单调试</a>
        </div>
    </div>

    <script src="assets/js/bootstrap.min.js"></script>
</body>
</html>
