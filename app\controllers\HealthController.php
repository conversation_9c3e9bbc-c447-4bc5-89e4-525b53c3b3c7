<?php
/**
 * 系统健康检查控制器
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../core/SystemHealthChecker.php';

class HealthController extends Controller
{
    private $healthChecker;

    public function __construct()
    {
        parent::__construct();
        $this->healthChecker = new SystemHealthChecker();
    }

    /**
     * 健康检查主页
     */
    public function index()
    {
        $this->requireAuth();
        
        // 执行快速检查
        $quickCheck = $this->healthChecker->quickCheck();
        
        $this->view('health/index', [
            'title' => '系统健康检查',
            'quickCheck' => $quickCheck
        ]);
    }

    /**
     * 执行完整检查
     */
    public function fullCheck()
    {
        $this->requireAuth();
        
        $report = $this->healthChecker->runFullCheck();
        
        $this->view('health/report', [
            'title' => '系统健康报告',
            'report' => $report
        ]);
    }

    /**
     * API接口 - 快速检查
     */
    public function apiQuickCheck()
    {
        header('Content-Type: application/json');
        
        $quickCheck = $this->healthChecker->quickCheck();
        
        echo json_encode([
            'status' => 'success',
            'data' => $quickCheck,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * API接口 - 完整检查
     */
    public function apiFullCheck()
    {
        header('Content-Type: application/json');
        
        $report = $this->healthChecker->runFullCheck();
        
        echo json_encode([
            'status' => 'success',
            'data' => $report,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 自动监控（定时任务调用）
     */
    public function monitor()
    {
        // 不需要登录验证，供定时任务调用
        
        $report = $this->healthChecker->runFullCheck();
        
        // 如果有严重问题，记录日志或发送通知
        if ($report['overall_status'] === 'fail') {
            $this->logCriticalIssue($report);
        }
        
        // 返回简单状态
        header('Content-Type: application/json');
        echo json_encode([
            'status' => $report['overall_status'],
            'summary' => $report['summary'],
            'timestamp' => $report['timestamp']
        ]);
    }

    /**
     * 记录严重问题
     */
    private function logCriticalIssue($report)
    {
        $logFile = __DIR__ . '/../../logs/health_issues.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logEntry = [
            'timestamp' => $report['timestamp'],
            'status' => $report['overall_status'],
            'failed_checks' => $report['summary']['failed'],
            'details' => []
        ];
        
        // 收集失败的检查项
        foreach ($report['details'] as $category => $checks) {
            foreach ($checks as $checkName => $check) {
                if ($check['status'] === 'fail') {
                    $logEntry['details'][] = [
                        'category' => $category,
                        'check' => $checkName,
                        'message' => $check['message']
                    ];
                }
            }
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
}
