<?php
/**
 * 运行所有功能测试
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '权限不足']);
    exit;
}

require_once '../../../app/core/Database.php';

header('Content-Type: application/json');

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true);
$mode = $input['mode'] ?? 'safe';
$iterations = $input['iterations'] ?? 1;

try {
    $db = Database::getInstance();
    $results = [];
    $startTime = microtime(true);
    
    // 定义测试模块
    $testModules = [
        'auth' => [
            'session_check' => '会话检查',
            'permission_check' => '权限检查'
        ],
        'materials' => [
            'list' => '列表查询',
            'search' => '搜索功能',
            'detail' => '详情查看'
        ],
        'products' => [
            'list' => '列表查询',
            'search' => '搜索功能',
            'detail' => '详情查看'
        ],
        'customers' => [
            'list' => '列表查询',
            'search' => '搜索功能',
            'detail' => '详情查看'
        ],
        'recipes' => [
            'list' => '列表查询',
            'detail' => '详情查看'
        ],
        'sales' => [
            'list' => '列表查询',
            'detail' => '详情查看'
        ],
        'inventory' => [
            'overview' => '库存概览',
            'logs' => '变动记录'
        ]
    ];
    
    // 运行测试
    foreach ($testModules as $module => $tests) {
        foreach ($tests as $testKey => $testName) {
            for ($i = 0; $i < $iterations; $i++) {
                $testStartTime = microtime(true);
                $testResult = runTest($db, $module, $testKey, $mode);
                $testDuration = (microtime(true) - $testStartTime) * 1000;
                
                $results[] = [
                    'id' => uniqid(),
                    'module' => $module,
                    'test' => $testName,
                    'status' => $testResult['status'],
                    'message' => $testResult['message'],
                    'duration' => round($testDuration, 2),
                    'iteration' => $i + 1,
                    'details' => $testResult['details'] ?? null
                ];
            }
        }
    }
    
    $totalTime = (microtime(true) - $startTime) * 1000;
    
    // 保存测试结果
    $debugDir = __DIR__ . '/..';
    if (!is_dir($debugDir . '/logs')) {
        mkdir($debugDir . '/logs', 0755, true);
    }
    
    $logFile = $debugDir . '/logs/test_results_' . date('Y-m-d_H-i-s') . '.json';
    file_put_contents($logFile, json_encode([
        'timestamp' => date('Y-m-d H:i:s'),
        'mode' => $mode,
        'iterations' => $iterations,
        'total_time' => $totalTime,
        'results' => $results
    ], JSON_PRETTY_PRINT));
    
    echo json_encode([
        'success' => true,
        'message' => '所有测试执行完成',
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'total_time' => round($totalTime, 2) . 'ms',
            'log_file' => basename($logFile)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '测试执行失败: ' . $e->getMessage()
    ]);
}

/**
 * 执行单个测试
 */
function runTest($db, $module, $test, $mode) {
    try {
        switch ($module) {
            case 'auth':
                return testAuth($test);
                
            case 'materials':
                return testMaterials($db, $test, $mode);
                
            case 'products':
                return testProducts($db, $test, $mode);
                
            case 'customers':
                return testCustomers($db, $test, $mode);
                
            case 'recipes':
                return testRecipes($db, $test, $mode);
                
            case 'sales':
                return testSales($db, $test, $mode);
                
            case 'inventory':
                return testInventory($db, $test, $mode);
                
            default:
                return ['status' => 'fail', 'message' => '未知模块'];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => $e->getMessage()];
    }
}

/**
 * 测试认证模块
 */
function testAuth($test) {
    switch ($test) {
        case 'session_check':
            return [
                'status' => isset($_SESSION['user_id']) ? 'pass' : 'fail',
                'message' => isset($_SESSION['user_id']) ? '会话正常' : '会话异常'
            ];
            
        case 'permission_check':
            return [
                'status' => ($_SESSION['role'] ?? '') === 'admin' ? 'pass' : 'warning',
                'message' => ($_SESSION['role'] ?? '') === 'admin' ? '管理员权限' : '非管理员权限'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试原材料模块
 */
function testMaterials($db, $test, $mode) {
    switch ($test) {
        case 'list':
            $result = $db->fetchAll("SELECT * FROM materials LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '查询到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'search':
            $result = $db->fetchAll("SELECT * FROM materials WHERE name LIKE '%糯%' LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '搜索到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'detail':
            $result = $db->fetch("SELECT * FROM materials ORDER BY id LIMIT 1");
            return [
                'status' => $result ? 'pass' : 'warning',
                'message' => $result ? '详情查询成功' : '无数据'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试成品模块
 */
function testProducts($db, $test, $mode) {
    switch ($test) {
        case 'list':
            $result = $db->fetchAll("SELECT * FROM products LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '查询到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'search':
            $result = $db->fetchAll("SELECT * FROM products WHERE name LIKE '%麻%' LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '搜索到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'detail':
            $result = $db->fetch("SELECT * FROM products ORDER BY id LIMIT 1");
            return [
                'status' => $result ? 'pass' : 'warning',
                'message' => $result ? '详情查询成功' : '无数据'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试客户模块
 */
function testCustomers($db, $test, $mode) {
    switch ($test) {
        case 'list':
            $result = $db->fetchAll("SELECT * FROM customers LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '查询到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'search':
            $result = $db->fetchAll("SELECT * FROM customers WHERE name LIKE '%客%' LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '搜索到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'detail':
            $result = $db->fetch("SELECT * FROM customers ORDER BY id LIMIT 1");
            return [
                'status' => $result ? 'pass' : 'warning',
                'message' => $result ? '详情查询成功' : '无数据'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试配方模块
 */
function testRecipes($db, $test, $mode) {
    switch ($test) {
        case 'list':
            $result = $db->fetchAll("SELECT * FROM recipes LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '查询到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'detail':
            $result = $db->fetch("SELECT * FROM recipes ORDER BY id LIMIT 1");
            return [
                'status' => $result ? 'pass' : 'warning',
                'message' => $result ? '详情查询成功' : '无数据'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试销售模块
 */
function testSales($db, $test, $mode) {
    switch ($test) {
        case 'list':
            $result = $db->fetchAll("SELECT * FROM sales LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '查询到 ' . count($result) . ' 条记录',
                'details' => ['count' => count($result)]
            ];
            
        case 'detail':
            $result = $db->fetch("SELECT * FROM sales ORDER BY id LIMIT 1");
            return [
                'status' => $result ? 'pass' : 'warning',
                'message' => $result ? '详情查询成功' : '无数据'
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}

/**
 * 测试库存模块
 */
function testInventory($db, $test, $mode) {
    switch ($test) {
        case 'overview':
            $materials = $db->fetch("SELECT COUNT(*) as count FROM materials");
            $products = $db->fetch("SELECT COUNT(*) as count FROM products");
            return [
                'status' => 'pass',
                'message' => '库存概览正常',
                'details' => [
                    'materials' => $materials['count'],
                    'products' => $products['count']
                ]
            ];
            
        case 'logs':
            $result = $db->fetchAll("SELECT * FROM inventory_logs LIMIT 5");
            return [
                'status' => 'pass',
                'message' => '变动记录查询正常',
                'details' => ['count' => count($result)]
            ];
            
        default:
            return ['status' => 'fail', 'message' => '未知测试'];
    }
}
?>
