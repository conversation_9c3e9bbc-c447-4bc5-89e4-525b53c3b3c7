<?php
/**
 * 销售模型
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Database.php';

class Sale
{
    private $db;
    private $table = 'sales';
    private $itemsTable = 'sale_items';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取所有销售记录
     */
    public function getAll($orderBy = 'created_at', $order = 'DESC', $limit = null)
    {
        $sql = "
            SELECT s.*, c.name as customer_name 
            FROM {$this->table} s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            ORDER BY {$orderBy} {$order}
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql);
    }

    /**
     * 根据ID获取销售记录
     */
    public function getById($id)
    {
        $sql = "
            SELECT s.*, c.name as customer_name, c.contact_info as customer_contact
            FROM {$this->table} s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            WHERE s.id = ? LIMIT 1
        ";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * 获取销售记录详情（包含销售项目）
     */
    public function getWithItems($saleId)
    {
        $sale = $this->getById($saleId);
        if (!$sale) {
            return null;
        }

        $items = $this->getSaleItems($saleId);
        $sale['items'] = $items;

        return $sale;
    }

    /**
     * 获取销售项目
     */
    public function getSaleItems($saleId)
    {
        $sql = "
            SELECT si.*, p.name as product_name, p.unit as product_unit
            FROM {$this->itemsTable} si 
            JOIN products p ON si.product_id = p.id 
            WHERE si.sale_id = ?
            ORDER BY p.name
        ";
        return $this->db->fetchAll($sql, [$saleId]);
    }

    /**
     * 创建销售记录
     */
    public function create($customerId, $items, $paymentAmount = 0, $notes = '')
    {
        try {
            $this->db->beginTransaction();

            // 计算总金额
            $totalAmount = 0;
            foreach ($items as $item) {
                $totalAmount += $item['quantity'] * $item['unit_price'];
            }

            // 创建销售主记录
            $sql = "INSERT INTO {$this->table} (customer_id, total_amount, payment_amount, notes, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())";
            $this->db->execute($sql, [$customerId, $totalAmount, $paymentAmount, $notes]);
            $saleId = $this->db->lastInsertId();

            // 添加销售项目
            $this->addSaleItems($saleId, $items);

            // 更新产品库存
            $this->updateProductStock($items, 'subtract');

            // 更新客户余额
            $balanceChange = $totalAmount - $paymentAmount;
            if ($balanceChange != 0) {
                $this->updateCustomerBalance($customerId, $balanceChange, 'add');
            }

            $this->db->commit();
            return $saleId;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 添加销售项目
     */
    private function addSaleItems($saleId, $items)
    {
        $sql = "INSERT INTO {$this->itemsTable} (sale_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)";
        
        foreach ($items as $item) {
            $this->db->execute($sql, [
                $saleId,
                $item['product_id'],
                $item['quantity'],
                $item['unit_price']
            ]);
        }
    }

    /**
     * 更新产品库存
     */
    private function updateProductStock($items, $operation = 'subtract')
    {
        foreach ($items as $item) {
            $sql = $operation === 'subtract' 
                ? "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?"
                : "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?";
            
            $this->db->execute($sql, [$item['quantity'], $item['product_id']]);
        }
    }

    /**
     * 更新客户余额
     */
    private function updateCustomerBalance($customerId, $amount, $operation = 'add')
    {
        $sql = $operation === 'add' 
            ? "UPDATE customers SET balance = balance + ? WHERE id = ?"
            : "UPDATE customers SET balance = balance - ? WHERE id = ?";
        
        $this->db->execute($sql, [$amount, $customerId]);
    }

    /**
     * 删除销售记录
     */
    public function delete($saleId)
    {
        try {
            $this->db->beginTransaction();

            // 获取销售记录详情
            $sale = $this->getWithItems($saleId);
            if (!$sale) {
                throw new Exception('销售记录不存在');
            }

            // 恢复产品库存
            $this->updateProductStock($sale['items'], 'add');

            // 恢复客户余额
            $balanceChange = $sale['total_amount'] - $sale['payment_amount'];
            if ($balanceChange != 0) {
                $this->updateCustomerBalance($sale['customer_id'], $balanceChange, 'subtract');
            }

            // 删除销售项目
            $this->db->execute("DELETE FROM {$this->itemsTable} WHERE sale_id = ?", [$saleId]);

            // 删除销售主记录
            $this->db->execute("DELETE FROM {$this->table} WHERE id = ?", [$saleId]);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 搜索销售记录
     */
    public function search($keyword, $startDate = null, $endDate = null)
    {
        $sql = "
            SELECT s.*, c.name as customer_name 
            FROM {$this->table} s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            WHERE (c.name LIKE ? OR s.notes LIKE ?)
        ";
        
        $params = ["%{$keyword}%", "%{$keyword}%"];
        
        if ($startDate) {
            $sql .= " AND DATE(s.created_at) >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $sql .= " AND DATE(s.created_at) <= ?";
            $params[] = $endDate;
        }
        
        $sql .= " ORDER BY s.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 获取统计信息
     */
    public function getStats($startDate = null, $endDate = null)
    {
        $stats = [];
        $whereClause = '';
        $params = [];
        
        if ($startDate && $endDate) {
            $whereClause = "WHERE DATE(created_at) BETWEEN ? AND ?";
            $params = [$startDate, $endDate];
        }
        
        // 总销售数量
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM {$this->table} {$whereClause}", $params);
        $stats['total_sales'] = $result['count'];
        
        // 总销售金额
        $result = $this->db->fetch("SELECT SUM(total_amount) as total FROM {$this->table} {$whereClause}", $params);
        $stats['total_amount'] = $result['total'] ?? 0;
        
        // 总收款金额
        $result = $this->db->fetch("SELECT SUM(payment_amount) as total FROM {$this->table} {$whereClause}", $params);
        $stats['total_payment'] = $result['total'] ?? 0;
        
        // 平均客单价
        $stats['average_amount'] = $stats['total_sales'] > 0 ? $stats['total_amount'] / $stats['total_sales'] : 0;
        
        return $stats;
    }

    /**
     * 获取今日销售统计
     */
    public function getTodayStats()
    {
        $today = date('Y-m-d');
        return $this->getStats($today, $today);
    }

    /**
     * 获取客户的购买历史
     */
    public function getCustomerHistory($customerId, $limit = 10)
    {
        $sql = "
            SELECT s.*, 
                   (SELECT COUNT(*) FROM {$this->itemsTable} WHERE sale_id = s.id) as items_count
            FROM {$this->table} s 
            WHERE s.customer_id = ? 
            ORDER BY s.created_at DESC 
            LIMIT ?
        ";
        return $this->db->fetchAll($sql, [$customerId, $limit]);
    }
}
