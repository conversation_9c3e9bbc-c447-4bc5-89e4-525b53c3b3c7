<?php
/**
 * 客户控制器
 * 麻糍工厂销售系统
 */

require_once __DIR__ . '/../core/Controller.php';
require_once __DIR__ . '/../models/Customer.php';

class CustomerController extends Controller
{
    private $customerModel;

    public function __construct()
    {
        parent::__construct();
        $this->customerModel = new Customer();
    }

    /**
     * 客户列表页
     */
    public function index()
    {
        $this->requireAuth();
        
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? 'all'; // all, debt, no_debt
        
        if (!empty($search)) {
            $customers = $this->customerModel->search($search);
        } else {
            $customers = $this->customerModel->getAll();
        }
        
        // 根据筛选条件过滤
        if ($filter === 'debt') {
            $customers = array_filter($customers, function($customer) {
                return $customer['balance'] > 0;
            });
        } elseif ($filter === 'no_debt') {
            $customers = array_filter($customers, function($customer) {
                return $customer['balance'] <= 0;
            });
        }
        
        $stats = $this->customerModel->getStats();
        
        $this->view('customers/index', [
            'title' => '客户管理',
            'customers' => $customers,
            'stats' => $stats,
            'search' => $search,
            'filter' => $filter
        ]);
    }

    /**
     * 显示新增客户页面
     */
    public function create()
    {
        $this->requireAuth();
        
        $this->view('customers/create', [
            'title' => '添加客户',
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理新增客户请求
     */
    public function store()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=customer&action=index');
        }

        $this->validateCsrfToken();

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '客户名称不能为空且不超过100个字符'
            ]
        ]);

        // 检查名称是否已存在
        if (empty($errors) && $this->customerModel->nameExists($_POST['name'])) {
            $errors['name'] = '该客户名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=customer&action=create');
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'contact_info' => trim($_POST['contact_info'] ?? ''),
                'balance' => floatval($_POST['balance'] ?? 0)
            ];

            $id = $this->customerModel->create($data);
            
            $this->setFlash('success', '客户添加成功');
            $this->redirect('index.php?controller=customer&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '添加失败：' . $e->getMessage());
            $this->redirect('index.php?controller=customer&action=create');
        }
    }

    /**
     * 显示编辑客户页面
     */
    public function edit()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $customer = $this->customerModel->getById($id);
        
        if (!$customer) {
            $this->setFlash('error', '客户不存在');
            $this->redirect('index.php?controller=customer&action=index');
        }

        $this->view('customers/edit', [
            'title' => '编辑客户',
            'customer' => $customer,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * 处理更新客户请求
     */
    public function update()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=customer&action=index');
        }

        $this->validateCsrfToken();

        $id = $_POST['id'] ?? 0;
        $customer = $this->customerModel->getById($id);
        
        if (!$customer) {
            $this->setFlash('error', '客户不存在');
            $this->redirect('index.php?controller=customer&action=index');
        }

        // 验证输入
        $errors = $this->validate($_POST, [
            'name' => [
                'required' => true,
                'max_length' => 100,
                'message' => '客户名称不能为空且不超过100个字符'
            ]
        ]);

        // 检查名称是否已存在（排除当前记录）
        if (empty($errors) && $this->customerModel->nameExists($_POST['name'], $id)) {
            $errors['name'] = '该客户名称已存在';
        }

        if (!empty($errors)) {
            $this->setFlash('error', implode('<br>', $errors));
            $this->redirect('index.php?controller=customer&action=edit&id=' . $id);
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'contact_info' => trim($_POST['contact_info'] ?? ''),
                'balance' => floatval($_POST['balance'] ?? 0)
            ];

            $this->customerModel->update($id, $data);
            
            $this->setFlash('success', '客户信息更新成功');
            $this->redirect('index.php?controller=customer&action=index');
            
        } catch (Exception $e) {
            $this->setFlash('error', '更新失败：' . $e->getMessage());
            $this->redirect('index.php?controller=customer&action=edit&id=' . $id);
        }
    }

    /**
     * 删除客户
     */
    public function delete()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $customer = $this->customerModel->getById($id);
        
        if (!$customer) {
            $this->setFlash('error', '客户不存在');
            $this->redirect('index.php?controller=customer&action=index');
        }

        try {
            $this->customerModel->delete($id);
            $this->setFlash('success', '客户删除成功');
        } catch (Exception $e) {
            $this->setFlash('error', '删除失败：' . $e->getMessage());
        }
        
        $this->redirect('index.php?controller=customer&action=index');
    }

    /**
     * 查看客户详情
     */
    public function detail()
    {
        $this->requireAuth();
        
        $id = $_GET['id'] ?? 0;
        $customer = $this->customerModel->getById($id);
        
        if (!$customer) {
            $this->setFlash('error', '客户不存在');
            $this->redirect('index.php?controller=customer&action=index');
        }

        // 获取销售历史（后续实现）
        $salesHistory = $this->customerModel->getSalesHistory($id);

        $this->view('customers/view', [
            'title' => '客户详情',
            'customer' => $customer,
            'salesHistory' => $salesHistory
        ]);
    }

    /**
     * 调整客户余额
     */
    public function adjustBalance()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?controller=customer&action=index');
        }

        $this->validateCsrfToken();

        $id = $_POST['customer_id'] ?? 0;
        $amount = floatval($_POST['amount'] ?? 0);
        $operation = $_POST['operation'] ?? 'set'; // set, add, subtract
        $description = trim($_POST['description'] ?? '');

        $customer = $this->customerModel->getById($id);
        if (!$customer) {
            $this->setFlash('error', '客户不存在');
            $this->redirect('index.php?controller=customer&action=index');
        }

        try {
            $this->customerModel->updateBalance($id, $amount, $operation);
            
            $operationText = [
                'set' => '设置为',
                'add' => '增加',
                'subtract' => '减少'
            ];
            
            $this->setFlash('success', "客户余额{$operationText[$operation]} ¥{$amount}");
            $this->redirect('index.php?controller=customer&action=detail&id=' . $id);
            
        } catch (Exception $e) {
            $this->setFlash('error', '余额调整失败：' . $e->getMessage());
            $this->redirect('index.php?controller=customer&action=detail&id=' . $id);
        }
    }
}
