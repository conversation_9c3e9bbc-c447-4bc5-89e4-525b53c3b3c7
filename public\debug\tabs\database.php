<?php
/**
 * 数据库调试标签
 */

session_start();
require_once '../../../app/core/Database.php';

$db = Database::getInstance();

// 获取所有表信息
$tables = [];
try {
    $tableList = $db->fetchAll("SHOW TABLES");
    foreach ($tableList as $table) {
        $tableName = array_values($table)[0];
        $tableInfo = $db->fetch("SELECT COUNT(*) as count FROM `$tableName`");
        $structure = $db->fetchAll("DESCRIBE `$tableName`");
        
        $tables[$tableName] = [
            'count' => $tableInfo['count'],
            'structure' => $structure
        ];
    }
} catch (Exception $e) {
    $dbError = $e->getMessage();
}

// 获取数据库状态
$dbStatus = [];
try {
    $dbStatus = $db->fetchAll("SHOW STATUS LIKE 'Connections'");
} catch (Exception $e) {
    // 忽略错误
}
?>

<div class="row">
    <!-- 数据库连接信息 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-database"></i> 数据库连接信息</h5>
            </div>
            <div class="card-body">
                <?php if (isset($dbError)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        连接失败: <?php echo htmlspecialchars($dbError); ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        数据库连接正常
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>数据库类型:</strong></td>
                            <td>MySQL</td>
                        </tr>
                        <tr>
                            <td><strong>字符集:</strong></td>
                            <td>UTF-8</td>
                        </tr>
                        <tr>
                            <td><strong>表数量:</strong></td>
                            <td><?php echo count($tables); ?></td>
                        </tr>
                        <tr>
                            <td><strong>总记录数:</strong></td>
                            <td><?php echo array_sum(array_column($tables, 'count')); ?></td>
                        </tr>
                    </table>
                <?php endif; ?>
                
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="testConnection()">
                        <i class="fas fa-sync"></i> 测试连接
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="showDatabaseInfo()">
                        <i class="fas fa-info"></i> 详细信息
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- SQL查询工具 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-terminal"></i> SQL查询工具</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <textarea class="form-control" id="sql-query" rows="4" 
                              placeholder="输入SQL查询语句...">SELECT COUNT(*) as total_users FROM users;</textarea>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success btn-sm" onclick="executeSql()">
                        <i class="fas fa-play"></i> 执行查询
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearSql()">
                        <i class="fas fa-eraser"></i> 清空
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-outline-info btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-list"></i> 示例查询
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="setSqlQuery('SELECT * FROM users LIMIT 5')">查看用户</a></li>
                            <li><a class="dropdown-item" href="#" onclick="setSqlQuery('SELECT * FROM materials LIMIT 5')">查看原材料</a></li>
                            <li><a class="dropdown-item" href="#" onclick="setSqlQuery('SELECT * FROM products LIMIT 5')">查看成品</a></li>
                            <li><a class="dropdown-item" href="#" onclick="setSqlQuery('SELECT COUNT(*) as count, \'materials\' as table_name FROM materials UNION SELECT COUNT(*) as count, \'products\' as table_name FROM products')">统计查询</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-exclamation-triangle"></i>
                        注意：仅支持SELECT查询，不允许修改数据
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表信息 -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-table"></i> 数据表信息</h5>
            </div>
            <div class="card-body">
                <?php if (isset($dbError)): ?>
                    <div class="alert alert-danger">无法获取表信息</div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>表名</th>
                                    <th>记录数</th>
                                    <th>字段数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables as $tableName => $tableInfo): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($tableName); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo number_format($tableInfo['count']); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo count($tableInfo['structure']); ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewTableStructure('<?php echo $tableName; ?>')">
                                                <i class="fas fa-eye"></i> 结构
                                            </button>
                                            <button class="btn btn-outline-success" onclick="viewTableData('<?php echo $tableName; ?>')">
                                                <i class="fas fa-list"></i> 数据
                                            </button>
                                            <button class="btn btn-outline-info" onclick="analyzeTable('<?php echo $tableName; ?>')">
                                                <i class="fas fa-chart-bar"></i> 分析
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 查询结果显示区域 -->
<div id="query-result" class="mt-3" style="display: none;"></div>

<!-- 表详情模态框 -->
<div class="modal fade" id="tableDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">表详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="table-detail-content">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>

<script>
// 测试数据库连接
function testConnection() {
    showQueryLoading('正在测试数据库连接...');
    
    fetch('../actions/test_database.php')
        .then(response => response.json())
        .then(data => {
            showQueryResult(data, '数据库连接测试');
        })
        .catch(error => {
            showQueryError('连接测试失败: ' + error.message);
        });
}

// 显示数据库详细信息
function showDatabaseInfo() {
    showQueryLoading('正在获取数据库信息...');
    
    fetch('../actions/database_info.php')
        .then(response => response.json())
        .then(data => {
            showQueryResult(data, '数据库详细信息');
        })
        .catch(error => {
            showQueryError('获取信息失败: ' + error.message);
        });
}

// 执行SQL查询
function executeSql() {
    const query = document.getElementById('sql-query').value.trim();
    
    if (!query) {
        alert('请输入SQL查询语句');
        return;
    }
    
    // 检查是否为SELECT查询
    if (!query.toLowerCase().startsWith('select')) {
        alert('仅支持SELECT查询语句');
        return;
    }
    
    showQueryLoading('正在执行查询...');
    
    fetch('../actions/execute_sql.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({query: query})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSqlResult(data.data, query);
        } else {
            showQueryError(data.message);
        }
    })
    .catch(error => {
        showQueryError('查询执行失败: ' + error.message);
    });
}

// 设置SQL查询
function setSqlQuery(query) {
    document.getElementById('sql-query').value = query;
}

// 清空SQL查询
function clearSql() {
    document.getElementById('sql-query').value = '';
}

// 查看表结构
function viewTableStructure(tableName) {
    fetch('../actions/table_structure.php?table=' + tableName)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTableStructure(tableName, data.data);
            } else {
                alert('获取表结构失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('获取表结构失败: ' + error.message);
        });
}

// 查看表数据
function viewTableData(tableName) {
    showQueryLoading('正在获取表数据...');
    
    fetch('../actions/table_data.php?table=' + tableName)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTableDataResult(tableName, data.data);
            } else {
                showQueryError('获取表数据失败: ' + data.message);
            }
        })
        .catch(error => {
            showQueryError('获取表数据失败: ' + error.message);
        });
}

// 分析表
function analyzeTable(tableName) {
    showQueryLoading('正在分析表...');
    
    fetch('../actions/analyze_table.php?table=' + tableName)
        .then(response => response.json())
        .then(data => {
            showQueryResult(data, `表分析结果: ${tableName}`);
        })
        .catch(error => {
            showQueryError('表分析失败: ' + error.message);
        });
}

// 显示查询加载状态
function showQueryLoading(message) {
    const resultDiv = document.getElementById('query-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    <div>${message}</div>
                </div>
            </div>
        </div>
    `;
}

// 显示查询结果
function showQueryResult(data, title) {
    const resultDiv = document.getElementById('query-result');
    const alertClass = data.success ? 'alert-success' : 'alert-danger';
    const icon = data.success ? 'fa-check-circle' : 'fa-times-circle';
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h6><i class="fas ${icon}"></i> ${title}</h6>
            </div>
            <div class="card-body">
                <div class="alert ${alertClass}">
                    ${data.message}
                </div>
    `;
    
    if (data.details) {
        html += '<div class="mt-2"><pre>' + JSON.stringify(data.details, null, 2) + '</pre></div>';
    }
    
    html += '</div></div>';
    resultDiv.innerHTML = html;
}

// 显示SQL查询结果
function showSqlResult(data, query) {
    const resultDiv = document.getElementById('query-result');
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-check-circle"></i> 查询结果</h6>
                <small class="text-muted">查询语句: ${query}</small>
            </div>
            <div class="card-body">
    `;
    
    if (Array.isArray(data) && data.length > 0) {
        html += '<div class="table-responsive"><table class="table table-sm table-striped">';
        
        // 表头
        html += '<thead><tr>';
        Object.keys(data[0]).forEach(key => {
            html += `<th>${key}</th>`;
        });
        html += '</tr></thead>';
        
        // 数据行
        html += '<tbody>';
        data.forEach(row => {
            html += '<tr>';
            Object.values(row).forEach(value => {
                html += `<td>${value !== null ? value : '<em>NULL</em>'}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody></table></div>';
        
        html += `<div class="mt-2"><small class="text-muted">共 ${data.length} 条记录</small></div>`;
    } else {
        html += '<div class="alert alert-info">查询无结果</div>';
    }
    
    html += '</div></div>';
    resultDiv.innerHTML = html;
}

// 显示表数据结果
function showTableDataResult(tableName, data) {
    const resultDiv = document.getElementById('query-result');
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-table"></i> 表数据: ${tableName}</h6>
            </div>
            <div class="card-body">
    `;
    
    if (Array.isArray(data) && data.length > 0) {
        html += '<div class="table-responsive"><table class="table table-sm table-hover">';
        
        // 表头
        html += '<thead class="table-dark"><tr>';
        Object.keys(data[0]).forEach(key => {
            html += `<th>${key}</th>`;
        });
        html += '</tr></thead>';
        
        // 数据行（只显示前20条）
        html += '<tbody>';
        data.slice(0, 20).forEach(row => {
            html += '<tr>';
            Object.values(row).forEach(value => {
                let displayValue = value;
                if (value === null) {
                    displayValue = '<em class="text-muted">NULL</em>';
                } else if (typeof value === 'string' && value.length > 50) {
                    displayValue = value.substring(0, 50) + '...';
                }
                html += `<td>${displayValue}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody></table></div>';
        
        if (data.length > 20) {
            html += `<div class="alert alert-info">显示前20条记录，共 ${data.length} 条</div>`;
        } else {
            html += `<div class="mt-2"><small class="text-muted">共 ${data.length} 条记录</small></div>`;
        }
    } else {
        html += '<div class="alert alert-info">表中无数据</div>';
    }
    
    html += '</div></div>';
    resultDiv.innerHTML = html;
}

// 显示表结构
function showTableStructure(tableName, structure) {
    const modalContent = document.getElementById('table-detail-content');
    
    let html = `
        <h6>表: ${tableName}</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>空值</th>
                        <th>键</th>
                        <th>默认值</th>
                        <th>额外</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    structure.forEach(field => {
        html += `
            <tr>
                <td><strong>${field.Field}</strong></td>
                <td><code>${field.Type}</code></td>
                <td>${field.Null}</td>
                <td>${field.Key ? '<span class="badge bg-primary">' + field.Key + '</span>' : ''}</td>
                <td>${field.Default !== null ? field.Default : '<em>NULL</em>'}</td>
                <td>${field.Extra || ''}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    modalContent.innerHTML = html;
    
    new bootstrap.Modal(document.getElementById('tableDetailModal')).show();
}

// 显示查询错误
function showQueryError(message) {
    const resultDiv = document.getElementById('query-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <div class="card">
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> 操作失败</h6>
                    <p>${message}</p>
                </div>
            </div>
        </div>
    `;
}
</script>
